<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #DC2626; font-weight: bold; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #059669; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .review-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #6B7280; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- Day 2 开场 -->
  <text x="960" y="180" class="title-text">Day 2 开场</text>
  
  <!-- Day 1 回顾 -->
  <rect x="150" y="220" width="1620" height="120" fill="#F9FAFB" stroke="#6B7280" stroke-width="2" rx="20"/>
  <circle cx="220" cy="260" r="15" fill="#6B7280"/>
  <text x="270" y="250" class="section-title" fill="#6B7280">Day 1 简要回顾</text>
  <text x="270" y="290" class="review-text">入网质量筑基 + 激活与活性管理 + 实战演练</text>
  
  <!-- 模块三标题 -->
  <text x="960" y="420" class="title-text">模块三概览</text>
  <text x="960" y="500" class="subtitle-text">异常处置与挽留 - 快速响应，有效挽留！</text>
  
  <!-- 核心内容 -->
  <rect x="150" y="540" width="1620" height="120" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="220" cy="580" r="15" fill="#059669"/>
  <text x="270" y="570" class="section-title">核心内容</text>
  <text x="270" y="610" class="content-text">流失预警、异常快反、挽留技巧</text>
  
  <!-- 学习目标 -->
  <rect x="150" y="680" width="1620" height="180" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="220" cy="740" r="15" fill="#2563EB"/>
  <text x="270" y="720" class="section-title">学习目标</text>
  <text x="320" y="760" class="content-text">• 会看预警，提前识别流失风险</text>
  <text x="320" y="800" class="content-text">• 懂处置流程，快速响应客户问题</text>
  <text x="320" y="840" class="content-text">• 掌握挽留方法，有效挽留客户</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 600)">
    <!-- Day 2 标识 -->
    <circle cx="0" cy="0" r="100" fill="none" stroke="#DC2626" stroke-width="4"/>
    <circle cx="0" cy="0" r="80" fill="#DC2626" opacity="0.1"/>
    
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="32" fill="#DC2626">Day 2</text>
    <text x="0" y="25" class="content-text" text-anchor="middle" font-size="24" fill="#DC2626">开始</text>
    
    <!-- 预警图标 -->
    <g transform="translate(0, 140)">
      <rect x="-40" y="-20" width="80" height="40" fill="#DC2626" opacity="0.2" rx="20"/>
      <text x="0" y="5" class="content-text" text-anchor="middle" font-size="18" fill="#DC2626">异常预警</text>
    </g>
  </g>
  
  <!-- 底部强调 -->
  <rect x="300" y="880" width="1320" height="80" fill="#FEF2F2" stroke="#DC2626" stroke-width="2" rx="15"/>
  <text x="960" y="910" class="content-text" text-anchor="middle" fill="#DC2626">关键：预防为主，快速响应</text>
  <text x="960" y="945" class="content-text" text-anchor="middle">让每一次异常都成为服务提升的机会</text>
</svg>
