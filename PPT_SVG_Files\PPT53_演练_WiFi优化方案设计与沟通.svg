<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .scenario-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #DC2626; font-weight: bold; }
      .task-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #059669; }
      .emphasis-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #8B5CF6; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">演练：WiFi优化方案设计与沟通</text>
  
  <!-- 场景设定 -->
  <rect x="100" y="220" width="1720" height="180" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="20" fill="#DC2626"/>
  <text x="180" y="300" class="content-text" text-anchor="middle" fill="white" font-size="20">场景</text>
  <text x="230" y="270" class="section-title" fill="#DC2626">演练场景：</text>
  <text x="230" y="320" class="scenario-text">提供一个复杂户型图和热力图</text>
  <text x="230" y="360" class="content-text">三室两厅，面积120㎡，存在多个WiFi盲区</text>
  
  <!-- 任务要求 -->
  <rect x="100" y="420" width="1720" height="280" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="520" r="20" fill="#059669"/>
  <text x="180" y="530" class="content-text" text-anchor="middle" fill="white" font-size="20">任务</text>
  <text x="230" y="500" class="section-title" fill="#059669">演练任务：</text>
  
  <g transform="translate(280, 540)">
    <circle cx="0" cy="0" r="12" fill="#059669"/>
    <text x="30" y="8" class="task-text">分组设计WiFi优化方案</text>
    <text x="50" y="38" class="emphasis-text">（Mesh组网优先推荐）</text>
    
    <circle cx="0" cy="70" r="12" fill="#059669"/>
    <text x="30" y="78" class="task-text">演练如何向客户解释和推荐</text>
    
    <circle cx="0" cy="110" r="12" fill="#059669"/>
    <text x="30" y="118" class="task-text">运用热力图等工具进行可视化沟通</text>
    
    <circle cx="0" cy="150" r="12" fill="#059669"/>
    <text x="30" y="158" class="task-text">制定具体的产品推荐和价格方案</text>
  </g>
  
  <!-- 演练要求 -->
  <rect x="100" y="720" width="1720" height="200" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="790" r="20" fill="#2563EB"/>
  <text x="180" y="800" class="content-text" text-anchor="middle" fill="white" font-size="20">要求</text>
  <text x="230" y="770" class="section-title" fill="#2563EB">演练要求：</text>
  
  <g transform="translate(280, 810)">
    <text x="0" y="0" class="content-text">• 每组20分钟讨论，8分钟展示</text>
    <text x="500" y="0" class="content-text">• 重点展示Mesh方案优势</text>
    
    <text x="0" y="40" class="content-text">• 体现专业性和说服力</text>
    <text x="500" y="40" class="content-text">• 包含价格和优惠策略</text>
    
    <text x="0" y="80" class="emphasis-text">关键：方案合理性 + 沟通技巧 + 销售能力</text>
  </g>
  
  <!-- 装饰元素 - 户型示意图 -->
  <g transform="translate(1400, 450)">
    <!-- 复杂户型轮廓 -->
    <rect x="0" y="0" width="400" height="300" fill="none" stroke="#6B7280" stroke-width="3" rx="10"/>
    
    <!-- 房间分割 -->
    <line x1="150" y1="0" x2="150" y2="300" stroke="#6B7280" stroke-width="2"/>
    <line x1="250" y1="0" x2="250" y2="300" stroke="#6B7280" stroke-width="2"/>
    <line x1="0" y1="120" x2="400" y2="120" stroke="#6B7280" stroke-width="2"/>
    <line x1="0" y1="200" x2="400" y2="200" stroke="#6B7280" stroke-width="2"/>
    
    <!-- 房间标签 -->
    <text x="75" y="60" class="content-text" text-anchor="middle" font-size="18">主卧</text>
    <text x="200" y="60" class="content-text" text-anchor="middle" font-size="18">次卧</text>
    <text x="325" y="60" class="content-text" text-anchor="middle" font-size="18">书房</text>
    <text x="75" y="160" class="content-text" text-anchor="middle" font-size="18">客厅</text>
    <text x="200" y="160" class="content-text" text-anchor="middle" font-size="18">餐厅</text>
    <text x="325" y="160" class="content-text" text-anchor="middle" font-size="18">厨房</text>
    <text x="75" y="250" class="content-text" text-anchor="middle" font-size="18">卫生间</text>
    <text x="200" y="250" class="content-text" text-anchor="middle" font-size="18">阳台</text>
    
    <!-- WiFi覆盖示意 -->
    <circle cx="200" cy="160" r="60" fill="#DC2626" opacity="0.3"/>
    <circle cx="200" cy="160" r="40" fill="#D97706" opacity="0.4"/>
    <circle cx="200" cy="160" r="20" fill="#059669" opacity="0.5"/>
    
    <!-- 路由器位置 -->
    <rect x="190" y="150" width="20" height="20" fill="#2563EB"/>
    <text x="200" y="340" class="content-text" text-anchor="middle" font-size="20">复杂户型示例</text>
  </g>
</svg>
