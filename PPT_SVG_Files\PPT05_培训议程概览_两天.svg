<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .day-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 44px; fill: #DC2626; font-weight: bold; }
      .module-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #059669; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="150" class="title-text">培训议程概览（两天）</text>
  
  <!-- Day 1 -->
  <rect x="80" y="200" width="1760" height="350" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <text x="150" y="250" class="day-title">Day 1: 质量筑基 &amp; 激活体验</text>
  
  <circle cx="200" cy="310" r="10" fill="#059669"/>
  <text x="240" y="330" class="module-title">模块一：入网质量 - 第一次就把事做对！</text>
  
  <circle cx="200" cy="380" r="10" fill="#059669"/>
  <text x="240" y="400" class="module-title">模块二：激活体验 - 72小时抓住客户的心！</text>
  
  <!-- Day 2 -->
  <rect x="80" y="570" width="1760" height="420" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <text x="150" y="620" class="day-title">Day 2: 难题化解 &amp; 价值挖掘</text>
  
  <circle cx="200" cy="680" r="10" fill="#059669"/>
  <text x="240" y="700" class="module-title">模块三：异常处置 - 快速响应，有效挽留！</text>
  
  <circle cx="200" cy="750" r="10" fill="#059669"/>
  <text x="240" y="770" class="module-title">模块四：价值深耕 - 不止是宽带，更是智慧家！</text>
  
  <circle cx="200" cy="820" r="10" fill="#059669"/>
  <text x="240" y="840" class="module-title">模块五：实战演练 - 学以致用，制定行动！</text>
  
  <!-- 时间轴装饰 -->
  <line x1="1600" y1="250" x2="1600" y2="850" stroke="#6B7280" stroke-width="4"/>
  <circle cx="1600" cy="250" r="12" fill="#DC2626"/>
  <circle cx="1600" cy="620" r="12" fill="#2563EB"/>
  <circle cx="1600" cy="850" r="12" fill="#059669"/>
  
  <text x="1650" y="260" class="content-text">开始</text>
  <text x="1650" y="630" class="content-text">Day 2</text>
  <text x="1650" y="860" class="content-text">结束</text>
</svg>
