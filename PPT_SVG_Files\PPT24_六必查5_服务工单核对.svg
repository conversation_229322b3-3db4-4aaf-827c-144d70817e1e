<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #DC2626; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .method-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #2563EB; font-weight: bold; }
      .item-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">六必查 5/6：服务工单核对</text>
  
  <!-- 查什么 -->
  <rect x="100" y="220" width="1720" height="240" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="300" r="15" fill="#059669"/>
  <text x="220" y="280" class="section-title">查什么：</text>
  <text x="220" y="330" class="content-text">客户信息、地址、电话、套餐、特殊要求</text>
  <text x="220" y="380" class="content-text">是否准确完整？</text>
  <text x="220" y="430" class="content-text">有无遗漏或错误信息？</text>
  
  <!-- 为何查 -->
  <rect x="100" y="480" width="1720" height="160" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="540" r="15" fill="#2563EB"/>
  <text x="220" y="520" class="section-title">为何查：</text>
  <text x="220" y="570" class="content-text">信息错误导致跑空、装错业务</text>
  <text x="220" y="610" class="content-text">浪费时间，影响客户满意度</text>
  
  <!-- 怎么查 -->
  <rect x="100" y="660" width="1720" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="730" r="15" fill="#D97706"/>
  <text x="220" y="710" class="section-title">怎么查：</text>
  <text x="220" y="760" class="method-text">仔细阅读工单所有字段</text>
  <text x="220" y="800" class="method-text">必要时提前电话确认</text>
  <text x="220" y="840" class="method-text">核对客户身份和需求</text>
  
  <!-- 装饰元素 - 工单示意图 -->
  <g transform="translate(1300, 350)">
    <!-- 工单表格 -->
    <rect x="0" y="0" width="500" height="400" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="3" rx="15"/>
    <text x="250" y="40" class="content-text" text-anchor="middle" font-size="28">服务工单</text>
    
    <!-- 工单内容 -->
    <g transform="translate(30, 70)">
      <text x="0" y="0" class="item-text">客户姓名：张先生</text>
      <text x="0" y="40" class="item-text">联系电话：138****1234</text>
      <text x="0" y="80" class="item-text">安装地址：成都市武侯区...</text>
      <text x="0" y="120" class="item-text">套餐类型：500M家庭宽带</text>
      <text x="0" y="160" class="item-text">特殊要求：需要WiFi6路由</text>
      <text x="0" y="200" class="item-text">预约时间：2024-08-24 14:00</text>
      <text x="0" y="240" class="item-text">工单状态：待处理</text>
    </g>
    
    <!-- 检查标记 -->
    <circle cx="450" cy="120" r="20" fill="#059669"/>
    <text x="450" y="130" class="content-text" text-anchor="middle" fill="white" font-size="20">✓</text>
    
    <circle cx="450" cy="180" r="20" fill="#059669"/>
    <text x="450" y="190" class="content-text" text-anchor="middle" fill="white" font-size="20">✓</text>
    
    <circle cx="450" cy="240" r="20" fill="#D97706"/>
    <text x="450" y="250" class="content-text" text-anchor="middle" fill="white" font-size="20">?</text>
  </g>
</svg>
