<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #374151; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="220" class="title-text">演练点评与分享</text>
  
  <!-- 各组展示 -->
  <rect x="150" y="280" width="1620" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="30"/>
  <circle cx="220" cy="350" r="20" fill="#059669"/>
  <text x="220" y="360" class="content-text" text-anchor="middle" fill="white" font-size="24">展示</text>
  <text x="270" y="330" class="section-title" fill="#059669">各组展示环节：</text>
  <text x="270" y="380" class="content-text">各组展示场景化营销方案与话术</text>
  <text x="270" y="420" class="content-text">重点分享创新思路和实用技巧</text>
  <text x="270" y="460" class="content-text">体现对目标群体的深度理解</text>
  
  <!-- 讲师点评 -->
  <rect x="150" y="500" width="1620" height="280" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="30"/>
  <circle cx="220" cy="600" r="20" fill="#2563EB"/>
  <text x="220" y="610" class="content-text" text-anchor="middle" fill="white" font-size="24">点评</text>
  <text x="270" y="570" class="section-title" fill="#2563EB">讲师点评重点：</text>
  
  <g transform="translate(320, 610)">
    <circle cx="0" cy="0" r="12" fill="#2563EB"/>
    <text x="30" y="8" class="content-text">创意性：方案是否有新颖的思路和亮点</text>
    
    <circle cx="0" cy="50" r="12" fill="#2563EB"/>
    <text x="30" y="58" class="content-text">实用性：方案是否具备可操作性和落地性</text>
    
    <circle cx="0" cy="100" r="12" fill="#2563EB"/>
    <text x="30" y="108" class="content-text">客户价值点：是否真正解决客户痛点</text>
    
    <circle cx="0" cy="150" r="12" fill="#2563EB"/>
    <text x="30" y="158" class="content-text">话术表达：是否生动有感染力</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1500, 550)">
    <!-- 分享交流图标 -->
    <circle cx="0" cy="0" r="100" fill="none" stroke="#059669" stroke-width="4"/>
    <circle cx="0" cy="0" r="70" fill="#059669" opacity="0.1"/>
    
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="28" fill="#059669">分享</text>
    <text x="0" y="25" class="content-text" text-anchor="middle" font-size="28" fill="#059669">交流</text>
    
    <!-- 互动元素 -->
    <circle cx="-60" cy="-60" r="20" fill="#2563EB" opacity="0.6"/>
    <text x="-60" y="-50" class="content-text" text-anchor="middle" fill="white" font-size="16">创意</text>
    
    <circle cx="60" cy="-60" r="20" fill="#D97706" opacity="0.6"/>
    <text x="60" y="-50" class="content-text" text-anchor="middle" fill="white" font-size="16">实用</text>
    
    <circle cx="-60" cy="60" r="20" fill="#DC2626" opacity="0.6"/>
    <text x="-60" y="70" class="content-text" text-anchor="middle" fill="white" font-size="16">价值</text>
    
    <circle cx="60" cy="60" r="20" fill="#8B5CF6" opacity="0.6"/>
    <text x="60" y="70" class="content-text" text-anchor="middle" fill="white" font-size="16">话术</text>
  </g>
  
  <!-- 底部总结 -->
  <rect x="200" y="800" width="1520" height="120" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="25"/>
  <text x="960" y="840" class="highlight-text" text-anchor="middle" fill="#D97706">优秀的场景化营销方案特点：</text>
  <text x="960" y="880" class="content-text" text-anchor="middle">深入洞察 + 创新思维 + 实用落地 + 生动表达</text>
  <text x="960" y="910" class="content-text" text-anchor="middle">让产品真正融入客户的生活场景</text>
</svg>
