<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #374151; }
      .strategy-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #8B5CF6; font-weight: bold; }
      .key-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #059669; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">对标分析：浙江移动"全光WiFi千兆小区"（精华）</text>
  
  <!-- 核心策略 -->
  <rect x="100" y="200" width="1720" height="280" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="310" r="20" fill="#8B5CF6"/>
  <text x="180" y="320" class="content-text" text-anchor="middle" fill="white" font-size="18">策略</text>
  <text x="230" y="260" class="section-title" fill="#8B5CF6">核心策略：</text>
  
  <g transform="translate(280, 300)">
    <circle cx="0" cy="0" r="10" fill="#8B5CF6"/>
    <text x="25" y="8" class="strategy-text">网络筑底（FTTR）</text>
    <text x="250" y="8" class="content-text">+ 产品融合（智家）</text>
    
    <circle cx="0" cy="50" r="10" fill="#8B5CF6"/>
    <text x="25" y="58" class="strategy-text">运营创新（精准/场景/服务升级）</text>
    <text x="450" y="58" class="content-text">+ 生态合作</text>
    
    <circle cx="0" cy="100" r="10" fill="#8B5CF6"/>
    <text x="25" y="108" class="content-text">四位一体，协同发力，打造差异化竞争优势</text>
  </g>
  
  <!-- 关键成功要素 -->
  <rect x="100" y="500" width="1720" height="240" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="590" r="20" fill="#059669"/>
  <text x="180" y="600" class="content-text" text-anchor="middle" fill="white" font-size="18">要素</text>
  <text x="230" y="560" class="section-title" fill="#059669">关键成功要素：</text>
  
  <g transform="translate(280, 600)">
    <circle cx="0" cy="0" r="8" fill="#059669"/>
    <text x="20" y="8" class="key-text">精准定位：聚焦高价值客户群体</text>
    
    <circle cx="0" cy="40" r="8" fill="#059669"/>
    <text x="20" y="48" class="key-text">高品质网络：FTTR全光网络保障</text>
    
    <circle cx="0" cy="80" r="8" fill="#059669"/>
    <text x="20" y="88" class="key-text">一体化服务：智家工程师专业队伍</text>
    
    <circle cx="0" cy="120" r="8" fill="#059669"/>
    <text x="20" y="128" class="key-text">生态协同：产业链合作伙伴支撑</text>
  </g>
  
  <!-- 装饰元素 - 浙江模式核心要素图 -->
  <g transform="translate(1400, 600)">
    <!-- 浙江模式框架 -->
    <rect x="0" y="0" width="400" height="300" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="200" y="30" class="content-text" text-anchor="middle" font-size="24">浙江模式核心要素</text>
    
    <!-- 四大要素 -->
    <g transform="translate(50, 60)">
      <!-- 网络筑底 -->
      <rect x="0" y="0" width="140" height="50" fill="#8B5CF6" opacity="0.6" rx="10"/>
      <text x="70" y="20" class="content-text" text-anchor="middle" fill="white" font-size="16">网络筑底</text>
      <text x="70" y="35" class="content-text" text-anchor="middle" fill="white" font-size="14">FTTR</text>
      
      <!-- 产品融合 -->
      <rect x="160" y="0" width="140" height="50" fill="#059669" opacity="0.6" rx="10"/>
      <text x="230" y="20" class="content-text" text-anchor="middle" fill="white" font-size="16">产品融合</text>
      <text x="230" y="35" class="content-text" text-anchor="middle" fill="white" font-size="14">智家</text>
      
      <!-- 运营创新 -->
      <rect x="0" y="70" width="140" height="50" fill="#2563EB" opacity="0.6" rx="10"/>
      <text x="70" y="90" class="content-text" text-anchor="middle" fill="white" font-size="16">运营创新</text>
      <text x="70" y="105" class="content-text" text-anchor="middle" fill="white" font-size="14">精准场景</text>
      
      <!-- 生态合作 -->
      <rect x="160" y="70" width="140" height="50" fill="#D97706" opacity="0.6" rx="10"/>
      <text x="230" y="90" class="content-text" text-anchor="middle" fill="white" font-size="16">生态合作</text>
      <text x="230" y="105" class="content-text" text-anchor="middle" fill="white" font-size="14">产业链</text>
      
      <!-- 中心连接 -->
      <circle cx="150" cy="60" r="30" fill="#DC2626" opacity="0.6"/>
      <text x="150" y="55" class="content-text" text-anchor="middle" fill="white" font-size="14">千兆</text>
      <text x="150" y="70" class="content-text" text-anchor="middle" fill="white" font-size="14">小区</text>
    </g>
    
    <text x="200" y="280" class="content-text" text-anchor="middle" font-size="20">标杆模式</text>
  </g>
</svg>
