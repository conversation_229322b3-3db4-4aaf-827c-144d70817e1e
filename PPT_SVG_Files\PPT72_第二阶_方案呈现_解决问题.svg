<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .phase-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #2563EB; font-weight: bold; text-anchor: middle; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #374151; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #059669; font-style: italic; }
      .tip-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #8B5CF6; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">第二阶：方案呈现（解决问题）</text>
  
  <!-- Step 3: 分析与诊断 -->
  <rect x="80" y="200" width="1760" height="140" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="15"/>
  <circle cx="150" cy="250" r="20" fill="#2563EB"/>
  <text x="150" y="260" class="content-text" text-anchor="middle" fill="white" font-size="18">3</text>
  <text x="190" y="240" class="step-title" fill="#2563EB">Step 3: 分析与诊断</text>
  <text x="190" y="270" class="content-text">快速判断问题原因</text>
  <text x="190" y="310" class="example-text">"根据系统看，可能是光纤老化导致的..."</text>
  
  <!-- Step 4: 方案制定 -->
  <rect x="80" y="360" width="1760" height="140" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="15"/>
  <circle cx="150" cy="410" r="20" fill="#2563EB"/>
  <text x="150" y="420" class="content-text" text-anchor="middle" fill="white" font-size="18">4</text>
  <text x="190" y="400" class="step-title" fill="#2563EB">Step 4: 方案制定</text>
  <text x="190" y="430" class="content-text">提供多个选择方案</text>
  <text x="190" y="470" class="example-text">"我们有几个方案：A免费更换光纤... B升级设备... C..."</text>
  
  <!-- Step 5: 价值重塑 -->
  <rect x="80" y="520" width="1760" height="140" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="15"/>
  <circle cx="150" cy="570" r="20" fill="#2563EB"/>
  <text x="150" y="580" class="content-text" text-anchor="middle" fill="white" font-size="18">5</text>
  <text x="190" y="560" class="step-title" fill="#2563EB">Step 5: 价值重塑</text>
  <text x="190" y="590" class="content-text">强调优势，给予补偿</text>
  <text x="190" y="630" class="example-text">"移动网络基础好...作为老用户，我们给您特别优惠..."</text>
  
  <!-- Step 6: 引导选择 -->
  <rect x="80" y="680" width="1760" height="140" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="15"/>
  <circle cx="150" cy="730" r="20" fill="#2563EB"/>
  <text x="150" y="740" class="content-text" text-anchor="middle" fill="white" font-size="18">6</text>
  <text x="190" y="720" class="step-title" fill="#2563EB">Step 6: 引导选择</text>
  <text x="190" y="750" class="content-text">促成决策</text>
  <text x="190" y="790" class="example-text">"您看哪个方案更合适？我推荐方案A，因为..."</text>
  
  <!-- 技巧提示 -->
  <rect x="200" y="840" width="1520" height="120" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <text x="960" y="880" class="tip-text" text-anchor="middle">技巧：方案具体可行，突出客户利益</text>
  <text x="960" y="910" class="content-text" text-anchor="middle">让客户感受到专业性和诚意</text>
  <text x="960" y="940" class="content-text" text-anchor="middle">给客户选择权，增强参与感</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <!-- 方案展示图标 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#2563EB" stroke-width="3" opacity="0.3"/>
    
    <!-- 方案选项 -->
    <rect x="-60" y="-60" width="40" height="30" fill="#2563EB" opacity="0.6" rx="5"/>
    <text x="-40" y="-40" class="content-text" text-anchor="middle" fill="white" font-size="16">A</text>
    
    <rect x="20" y="-60" width="40" height="30" fill="#059669" opacity="0.6" rx="5"/>
    <text x="40" y="-40" class="content-text" text-anchor="middle" fill="white" font-size="16">B</text>
    
    <rect x="-60" y="30" width="40" height="30" fill="#D97706" opacity="0.6" rx="5"/>
    <text x="-40" y="50" class="content-text" text-anchor="middle" fill="white" font-size="16">C</text>
    
    <rect x="20" y="30" width="40" height="30" fill="#DC2626" opacity="0.6" rx="5"/>
    <text x="40" y="50" class="content-text" text-anchor="middle" fill="white" font-size="16">D</text>
    
    <text x="0" y="100" class="content-text" text-anchor="middle" font-size="20" fill="#2563EB">多方案选择</text>
  </g>
</svg>
