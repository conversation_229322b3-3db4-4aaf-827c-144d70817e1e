<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .principle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">临危处置：异常事件快速响应</text>
  
  <!-- 目标 -->
  <rect x="100" y="240" width="1720" height="200" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="320" r="20" fill="#2563EB"/>
  <text x="180" y="330" class="content-text" text-anchor="middle" fill="white" font-size="20">目标</text>
  <text x="230" y="300" class="section-title" fill="#2563EB">处置目标：</text>
  <text x="230" y="350" class="content-text">问题发生时，快速、规范、有效地处理</text>
  <text x="230" y="390" class="content-text">降低负面影响，维护客户关系</text>
  <text x="230" y="430" class="highlight-text">化危机为转机</text>
  
  <!-- 原则 -->
  <rect x="100" y="460" width="1720" height="320" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="580" r="20" fill="#059669"/>
  <text x="180" y="590" class="content-text" text-anchor="middle" fill="white" font-size="20">原则</text>
  <text x="230" y="540" class="section-title" fill="#059669">处置原则：</text>
  
  <g transform="translate(280, 580)">
    <circle cx="0" cy="0" r="12" fill="#059669"/>
    <text x="30" y="8" class="principle-text" fill="#059669">先恢复，后优化</text>
    <text x="350" y="8" class="content-text">优先解决客户问题，再分析根因</text>
    
    <circle cx="0" cy="60" r="12" fill="#059669"/>
    <text x="30" y="68" class="principle-text" fill="#059669">先处理，后分析</text>
    <text x="350" y="68" class="content-text">快速响应客户需求，避免情绪升级</text>
    
    <circle cx="0" cy="120" r="12" fill="#059669"/>
    <text x="30" y="128" class="principle-text" fill="#059669">客户感知第一</text>
    <text x="350" y="128" class="content-text">以客户体验为中心，主动沟通</text>
    
    <circle cx="0" cy="180" r="12" fill="#059669"/>
    <text x="30" y="188" class="highlight-text">时间就是生命线</text>
    <text x="350" y="188" class="content-text">每分每秒都关系到客户满意度</text>
  </g>
  
  <!-- 装饰元素 - 快速响应示意图 -->
  <g transform="translate(1600, 500)">
    <!-- 响应时钟 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#DC2626" stroke-width="4"/>
    <circle cx="0" cy="0" r="60" fill="#DC2626" opacity="0.1"/>
    
    <!-- 时钟指针 -->
    <line x1="0" y1="0" x2="0" y2="-50" stroke="#DC2626" stroke-width="4"/>
    <line x1="0" y1="0" x2="35" y2="0" stroke="#DC2626" stroke-width="3"/>
    <circle cx="0" cy="0" r="8" fill="#DC2626"/>
    
    <!-- 快速标识 -->
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="24" fill="#DC2626">快速</text>
    <text x="0" y="15" class="content-text" text-anchor="middle" font-size="24" fill="#DC2626">响应</text>
    
    <!-- 响应箭头 -->
    <g transform="translate(0, 120)">
      <path d="M -40 0 L 40 0" stroke="#059669" stroke-width="4" marker-end="url(#arrowhead)"/>
      <text x="0" y="30" class="content-text" text-anchor="middle" font-size="18" fill="#059669">立即行动</text>
    </g>
  </g>
  
  <!-- 底部强调 -->
  <rect x="200" y="820" width="1520" height="120" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <text x="960" y="860" class="highlight-text" text-anchor="middle">关键：速度决定成败</text>
  <text x="960" y="900" class="content-text" text-anchor="middle">快速响应是客户满意度的基础</text>
  <text x="960" y="930" class="content-text" text-anchor="middle">每一次异常都是展示专业能力的机会</text>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#059669"/>
    </marker>
  </defs>
</svg>
