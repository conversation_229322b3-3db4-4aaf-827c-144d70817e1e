<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #2563EB; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .tool-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #DC2626; font-weight: bold; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #059669; font-style: italic; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">第三次握手：交付后（验收确认）</text>
  
  <!-- 时机 -->
  <rect x="100" y="220" width="1720" height="140" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="270" r="15" fill="#2563EB"/>
  <text x="220" y="250" class="section-title">时机：</text>
  <text x="220" y="290" class="content-text">全部完成，测速达标，WiFi覆盖测试后</text>
  <text x="220" y="330" class="content-text">确保客户满意，获得正面评价</text>
  
  <!-- 工具 -->
  <rect x="100" y="380" width="1720" height="140" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="430" r="15" fill="#DC2626"/>
  <text x="220" y="410" class="section-title">工具：</text>
  <text x="220" y="450" class="tool-text">测速截图 + NPS评价二维码 + 服务确认单</text>
  <text x="220" y="490" class="content-text">用数据说话，引导正面评价</text>
  
  <!-- 内容 -->
  <rect x="100" y="540" width="1720" height="180" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="600" r="15" fill="#059669"/>
  <text x="220" y="580" class="section-title">确认内容：</text>
  <text x="270" y="620" class="content-text">• 展示测速结果和覆盖效果</text>
  <text x="270" y="660" class="content-text">• 讲解使用方法和注意事项</text>
  <text x="270" y="700" class="content-text">• 邀请客户评价和确认满意度</text>
  
  <!-- 话术 -->
  <rect x="100" y="740" width="1720" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="800" r="15" fill="#D97706"/>
  <text x="220" y="780" class="section-title">标准话术：</text>
  <text x="220" y="820" class="speech-text">"李先生，您看测速结果520M，WiFi全屋覆盖都很好。</text>
  <text x="220" y="860" class="speech-text">这是使用说明，有问题随时联系。麻烦您扫码做个评价，</text>
  <text x="220" y="900" class="speech-text">9-10分是我们的目标！感谢支持！"</text>
  
  <!-- 装饰元素 - 验收清单 -->
  <g transform="translate(1400, 350)">
    <!-- 验收单 -->
    <rect x="0" y="0" width="400" height="400" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="200" y="40" class="content-text" text-anchor="middle" font-size="24">验收确认单</text>
    
    <!-- 验收项目 -->
    <g transform="translate(30, 80)">
      <circle cx="0" cy="0" r="12" fill="#059669"/>
      <text x="0" y="6" class="content-text" text-anchor="middle" fill="white" font-size="16">✓</text>
      <text x="30" y="8" class="content-text" font-size="20">网速达标：520M</text>
      
      <circle cx="0" cy="50" r="12" fill="#059669"/>
      <text x="0" y="56" class="content-text" text-anchor="middle" fill="white" font-size="16">✓</text>
      <text x="30" y="58" class="content-text" font-size="20">WiFi覆盖良好</text>
      
      <circle cx="0" cy="100" r="12" fill="#059669"/>
      <text x="0" y="106" class="content-text" text-anchor="middle" fill="white" font-size="16">✓</text>
      <text x="30" y="108" class="content-text" font-size="20">设备正常运行</text>
      
      <circle cx="0" cy="150" r="12" fill="#059669"/>
      <text x="0" y="156" class="content-text" text-anchor="middle" fill="white" font-size="16">✓</text>
      <text x="30" y="158" class="content-text" font-size="20">客户使用培训</text>
      
      <!-- 二维码区域 -->
      <rect x="0" y="200" width="340" height="120" fill="#E0F2FE" stroke="#0284C7" stroke-width="2" rx="10"/>
      <text x="170" y="230" class="content-text" text-anchor="middle" font-size="18">NPS评价二维码</text>
      <rect x="140" y="240" width="60" height="60" fill="#6B7280"/>
      <text x="170" y="275" class="content-text" text-anchor="middle" fill="white" font-size="12">QR</text>
    </g>
  </g>
</svg>
