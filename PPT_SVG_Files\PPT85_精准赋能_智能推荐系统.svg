<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .purpose-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #8B5CF6; font-weight: bold; }
      .compare-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">精准赋能：智能推荐系统</text>
  
  <!-- 目的 -->
  <rect x="100" y="240" width="1720" height="200" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="320" r="20" fill="#8B5CF6"/>
  <text x="180" y="330" class="content-text" text-anchor="middle" fill="white" font-size="20">目的</text>
  <text x="230" y="300" class="section-title" fill="#8B5CF6">系统目的：</text>
  <text x="230" y="350" class="purpose-text">在合适时机、通过合适渠道</text>
  <text x="230" y="390" class="purpose-text">向合适用户推荐合适产品</text>
  <text x="230" y="430" class="content-text">实现精准营销，提升转化率</text>
  
  <!-- 对比 -->
  <rect x="100" y="460" width="1720" height="320" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="580" r="20" fill="#2563EB"/>
  <text x="180" y="590" class="content-text" text-anchor="middle" fill="white" font-size="20">对比</text>
  <text x="230" y="540" class="section-title" fill="#2563EB">模式对比：</text>
  
  <!-- 传统模式 -->
  <g transform="translate(280, 580)">
    <rect x="0" y="0" width="600" height="80" fill="#FEE2E2" stroke="#DC2626" stroke-width="2" rx="10"/>
    <text x="20" y="30" class="compare-text" fill="#DC2626">传统广撒网模式：</text>
    <text x="20" y="60" class="content-text" font-size="26">同样的产品推给所有用户，转化率低，成本高</text>
  </g>
  
  <!-- VS -->
  <text x="960" y="650" class="section-title" text-anchor="middle" fill="#6B7280">VS</text>
  
  <!-- 智能模式 -->
  <g transform="translate(280, 680)">
    <rect x="0" y="0" width="600" height="80" fill="#DCFCE7" stroke="#059669" stroke-width="2" rx="10"/>
    <text x="20" y="30" class="compare-text" fill="#059669">智能精准滴灌：</text>
    <text x="20" y="60" class="content-text" font-size="26">基于用户画像，个性化推荐，转化率高，ROI好</text>
  </g>
  
  <!-- 装饰元素 - 智能推荐示意图 -->
  <g transform="translate(1400, 500)">
    <!-- 推荐系统框架 -->
    <rect x="0" y="0" width="400" height="300" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="200" y="30" class="content-text" text-anchor="middle" font-size="24">智能推荐系统</text>
    
    <!-- 用户输入 -->
    <g transform="translate(50, 60)">
      <circle cx="0" cy="0" r="30" fill="#2563EB" opacity="0.6"/>
      <text x="0" y="5" class="content-text" text-anchor="middle" fill="white" font-size="16">用户A</text>
      
      <circle cx="100" cy="0" r="30" fill="#059669" opacity="0.6"/>
      <text x="100" y="5" class="content-text" text-anchor="middle" fill="white" font-size="16">用户B</text>
      
      <circle cx="200" cy="0" r="30" fill="#D97706" opacity="0.6"/>
      <text x="200" y="5" class="content-text" text-anchor="middle" fill="white" font-size="16">用户C</text>
    </g>
    
    <!-- 智能分析 -->
    <rect x="50" y="120" width="300" height="60" fill="#8B5CF6" opacity="0.2" rx="10"/>
    <text x="200" y="140" class="content-text" text-anchor="middle" font-size="20" fill="#8B5CF6">智能分析引擎</text>
    <text x="200" y="165" class="content-text" text-anchor="middle" font-size="16">画像匹配+行为预测</text>
    
    <!-- 个性化推荐 -->
    <g transform="translate(50, 200)">
      <rect x="0" y="0" width="80" height="40" fill="#2563EB" opacity="0.6" rx="5"/>
      <text x="40" y="25" class="content-text" text-anchor="middle" fill="white" font-size="14">智能家居</text>
      
      <rect x="100" y="0" width="80" height="40" fill="#059669" opacity="0.6" rx="5"/>
      <text x="140" y="25" class="content-text" text-anchor="middle" fill="white" font-size="14">教育套餐</text>
      
      <rect x="200" y="0" width="80" height="40" fill="#D97706" opacity="0.6" rx="5"/>
      <text x="240" y="25" class="content-text" text-anchor="middle" fill="white" font-size="14">影音服务</text>
    </g>
    
    <text x="200" y="280" class="content-text" text-anchor="middle" font-size="20">千人千面</text>
  </g>
</svg>
