<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .purpose-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #8B5CF6; font-weight: bold; }
      .core-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 38px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="200" class="title-text">深耕区域：网格化作战体系</text>
  
  <!-- 目的 -->
  <rect x="150" y="260" width="1620" height="240" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="30"/>
  <circle cx="220" cy="350" r="20" fill="#8B5CF6"/>
  <text x="220" y="360" class="content-text" text-anchor="middle" fill="white" font-size="24">目的</text>
  <text x="270" y="320" class="section-title" fill="#8B5CF6">建设目的：</text>
  <text x="270" y="370" class="purpose-text">打破部门墙，责任到格</text>
  <text x="270" y="420" class="purpose-text">精准服务，高效运营</text>
  <text x="270" y="470" class="content-text">实现区域精细化管理和协同作战</text>
  
  <!-- 核心 -->
  <rect x="150" y="520" width="1620" height="200" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="30"/>
  <circle cx="220" cy="590" r="20" fill="#DC2626"/>
  <text x="220" y="600" class="content-text" text-anchor="middle" fill="white" font-size="24">核心</text>
  <text x="270" y="570" class="section-title" fill="#DC2626">体系核心：</text>
  <text x="270" y="620" class="core-text">网格经理 + "蜂巢"系统</text>
  <text x="270" y="670" class="content-text">人员组织 + 数字化工具双轮驱动</text>
  <text x="270" y="700" class="content-text">实现网格内全要素统一管理</text>
  
  <!-- 装饰元素 - 网格化示意图 -->
  <g transform="translate(1400, 500)">
    <!-- 网格结构 -->
    <rect x="0" y="0" width="400" height="300" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="200" y="30" class="content-text" text-anchor="middle" font-size="24">网格化作战体系</text>
    
    <!-- 网格划分 -->
    <g transform="translate(50, 60)">
      <!-- 网格线 -->
      <line x1="0" y1="0" x2="300" y2="0" stroke="#6B7280" stroke-width="1"/>
      <line x1="0" y1="80" x2="300" y2="80" stroke="#6B7280" stroke-width="1"/>
      <line x1="0" y1="160" x2="300" y2="160" stroke="#6B7280" stroke-width="1"/>
      <line x1="0" y1="240" x2="300" y2="240" stroke="#6B7280" stroke-width="1"/>
      
      <line x1="0" y1="0" x2="0" y2="240" stroke="#6B7280" stroke-width="1"/>
      <line x1="100" y1="0" x2="100" y2="240" stroke="#6B7280" stroke-width="1"/>
      <line x1="200" y1="0" x2="200" y2="240" stroke="#6B7280" stroke-width="1"/>
      <line x1="300" y1="0" x2="300" y2="240" stroke="#6B7280" stroke-width="1"/>
      
      <!-- 网格经理 -->
      <circle cx="50" cy="40" r="15" fill="#DC2626" opacity="0.8"/>
      <text x="50" y="45" class="content-text" text-anchor="middle" fill="white" font-size="12">经理</text>
      
      <circle cx="150" cy="40" r="15" fill="#DC2626" opacity="0.8"/>
      <text x="150" y="45" class="content-text" text-anchor="middle" fill="white" font-size="12">经理</text>
      
      <circle cx="250" cy="40" r="15" fill="#DC2626" opacity="0.8"/>
      <text x="250" y="45" class="content-text" text-anchor="middle" fill="white" font-size="12">经理</text>
      
      <!-- 蜂巢系统 -->
      <rect x="50" y="120" width="200" height="40" fill="#8B5CF6" opacity="0.6" rx="10"/>
      <text x="150" y="145" class="content-text" text-anchor="middle" fill="white" font-size="18">蜂巢系统</text>
      
      <!-- 服务要素 -->
      <circle cx="50" cy="200" r="12" fill="#059669" opacity="0.6"/>
      <text x="50" y="205" class="content-text" text-anchor="middle" fill="white" font-size="10">用户</text>
      
      <circle cx="150" cy="200" r="12" fill="#2563EB" opacity="0.6"/>
      <text x="150" y="205" class="content-text" text-anchor="middle" fill="white" font-size="10">资源</text>
      
      <circle cx="250" cy="200" r="12" fill="#D97706" opacity="0.6"/>
      <text x="250" y="205" class="content-text" text-anchor="middle" fill="white" font-size="10">服务</text>
    </g>
    
    <text x="200" y="280" class="content-text" text-anchor="middle" font-size="20">精准作战</text>
  </g>
</svg>
