<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .key-point { font-family: 'Microsoft YaHei', sans-serif; font-size: 38px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  <path d="M 100 960 Q 960 1060 1820 960" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">模块三总结：关键要点回顾</text>
  
  <!-- 要点1 -->
  <rect x="100" y="240" width="1720" height="120" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="280" r="20" fill="#DC2626"/>
  <text x="180" y="290" class="content-text" text-anchor="middle" fill="white" font-size="24">1</text>
  <text x="230" y="290" class="key-point" fill="#DC2626">流失预警早发现，主动干预</text>
  <text x="230" y="330" class="content-text">健康度雷达图+预警标签，防患于未然</text>
  
  <!-- 要点2 -->
  <rect x="100" y="380" width="1720" height="120" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="420" r="20" fill="#2563EB"/>
  <text x="180" y="430" class="content-text" text-anchor="middle" fill="white" font-size="24">2</text>
  <text x="230" y="430" class="key-point" fill="#2563EB">异常处置快准稳，客户优先</text>
  <text x="230" y="470" class="content-text">五步流程标准化，专业工具提效率</text>
  
  <!-- 要点3 -->
  <rect x="100" y="520" width="1720" height="120" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="560" r="20" fill="#059669"/>
  <text x="180" y="570" class="content-text" text-anchor="middle" fill="white" font-size="24">3</text>
  <text x="230" y="570" class="key-point" fill="#059669">三阶八步挽留法，情理并重</text>
  <text x="230" y="610" class="content-text">共情接纳→方案呈现→闭环确认</text>
  
  <!-- 要点4 -->
  <rect x="100" y="660" width="1720" height="120" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="700" r="20" fill="#D97706"/>
  <text x="180" y="710" class="content-text" text-anchor="middle" fill="white" font-size="24">4</text>
  <text x="230" y="710" class="key-point" fill="#D97706">善用工具提效率，精准施策</text>
  <text x="230" y="750" class="content-text">挽留工具箱+决策引擎，智能化挽留</text>
  
  <!-- 核心理念 -->
  <rect x="200" y="800" width="1520" height="120" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <text x="960" y="840" class="key-point" text-anchor="middle" fill="#8B5CF6">核心理念：化危机为转机</text>
  <text x="960" y="880" class="content-text" text-anchor="middle">每一次异常都是展示专业能力的机会</text>
  <text x="960" y="910" class="content-text" text-anchor="middle">让客户因为这次经历而更加信任我们</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <circle cx="0" cy="0" r="80" fill="none" stroke="#3B82F6" stroke-width="4" opacity="0.3"/>
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="28">模块三</text>
    <text x="0" y="20" class="content-text" text-anchor="middle" font-size="28">完成</text>
    <circle cx="0" cy="0" r="50" fill="#2563EB" opacity="0.2"/>
    
    <!-- 挽留成功图标 -->
    <g transform="translate(0, 120)">
      <circle cx="0" cy="0" r="40" fill="none" stroke="#059669" stroke-width="3"/>
      <path d="M -15 0 L -5 10 L 15 -10" stroke="#059669" stroke-width="4" fill="none"/>
      <text x="0" y="60" class="content-text" text-anchor="middle" font-size="18" fill="#059669">挽留成功</text>
    </g>
  </g>
</svg>
