<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; fill: #8B5CF6; text-anchor: middle; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #374151; }
      .thinking-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #059669; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 200 Q 960 100 1820 200" class="accent-curve" opacity="0.4"/>
  <path d="M 100 880 Q 960 980 1820 880" class="accent-curve" opacity="0.4"/>
  
  <!-- 主标题 -->
  <text x="960" y="280" class="title-text">讨论：网格化对我的工作意味着什么？</text>
  
  <!-- 副标题 -->
  <text x="960" y="360" class="subtitle-text">引导学员思考网格化带来的机遇和挑战</text>
  
  <!-- 思考引导 -->
  <rect x="300" y="420" width="1320" height="300" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="30"/>
  <text x="960" y="480" class="thinking-text" text-anchor="middle">思考方向：</text>
  
  <g transform="translate(400, 520)">
    <circle cx="0" cy="0" r="12" fill="#059669"/>
    <text x="30" y="8" class="content-text">机遇：网格化能给我带来什么新机会？</text>
    
    <circle cx="0" cy="60" r="12" fill="#059669"/>
    <text x="30" y="68" class="content-text">挑战：我需要适应哪些新的工作方式？</text>
    
    <circle cx="0" cy="120" r="12" fill="#059669"/>
    <text x="30" y="128" class="content-text">能力：我需要提升哪些新技能？</text>
    
    <circle cx="0" cy="180" r="12" fill="#059669"/>
    <text x="30" y="188" class="content-text">协作：如何与网格经理更好配合？</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(400, 750)">
    <!-- 思考泡泡图标 -->
    <circle cx="0" cy="0" r="60" fill="#8B5CF6" opacity="0.2"/>
    <text x="0" y="10" class="title-text" text-anchor="middle" font-size="60" fill="#8B5CF6">💭</text>
  </g>
  
  <g transform="translate(960, 750)">
    <!-- 讨论图标 -->
    <circle cx="0" cy="0" r="60" fill="#059669" opacity="0.2"/>
    <text x="0" y="10" class="title-text" text-anchor="middle" font-size="60" fill="#059669">💬</text>
  </g>
  
  <g transform="translate(1520, 750)">
    <!-- 行动图标 -->
    <circle cx="0" cy="0" r="60" fill="#DC2626" opacity="0.2"/>
    <text x="0" y="10" class="title-text" text-anchor="middle" font-size="60" fill="#DC2626">🚀</text>
  </g>
  
  <!-- 底部说明 -->
  <text x="960" y="850" class="content-text" text-anchor="middle">小组讨论 5 分钟，然后分享交流</text>
</svg>
