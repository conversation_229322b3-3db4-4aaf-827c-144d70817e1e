<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .data-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .insight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">案例分享：绵阳72小时激活实践</text>
  
  <!-- 做法 -->
  <rect x="100" y="220" width="1720" height="200" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="20" fill="#2563EB"/>
  <text x="180" y="300" class="content-text" text-anchor="middle" fill="white" font-size="24">做法</text>
  <text x="230" y="270" class="section-title" fill="#2563EB">实践做法：</text>
  <text x="230" y="320" class="content-text">严格执行72小时SOP标准流程</text>
  <text x="230" y="360" class="content-text">利用大数据分析，精准推荐个性化产品</text>
  <text x="230" y="400" class="content-text">建立完善的用户行为跟踪体系</text>
  
  <!-- 成效 -->
  <rect x="100" y="440" width="1720" height="200" fill="#DCFCE7" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="510" r="20" fill="#059669"/>
  <text x="180" y="520" class="content-text" text-anchor="middle" fill="white" font-size="24">成效</text>
  <text x="230" y="490" class="section-title" fill="#059669">显著成效：</text>
  <text x="230" y="540" class="data-text" fill="#059669">新用户首月增值渗透率大幅提升</text>
  <text x="230" y="580" class="content-text">用户活跃度提升65%，满意度显著改善</text>
  <text x="230" y="620" class="content-text">客户生命周期价值持续增长</text>
  
  <!-- 启示 -->
  <rect x="100" y="660" width="1720" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="730" r="20" fill="#D97706"/>
  <text x="180" y="740" class="content-text" text-anchor="middle" fill="white" font-size="24">启示</text>
  <text x="230" y="710" class="section-title" fill="#D97706">关键启示：</text>
  <text x="230" y="760" class="insight-text">主动关怀 + 精准推荐 = 价值提升</text>
  <text x="230" y="800" class="content-text">黄金时间窗口的把握至关重要</text>
  <text x="230" y="840" class="content-text">数据驱动的个性化服务是核心竞争力</text>
  
  <!-- 装饰元素 - 成效图表 -->
  <g transform="translate(1400, 450)">
    <!-- 提升效果图 -->
    <rect x="0" y="0" width="400" height="250" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="200" y="30" class="content-text" text-anchor="middle" font-size="24">绵阳实践成效</text>
    
    <!-- 渗透率提升 -->
    <rect x="50" y="60" width="60" height="120" fill="#DC2626" opacity="0.3"/>
    <rect x="50" y="120" width="60" height="60" fill="#DC2626"/>
    <text x="80" y="200" class="content-text" text-anchor="middle" font-size="18">实施前</text>
    
    <!-- 箭头 -->
    <path d="M 130 120 L 170 120" stroke="#059669" stroke-width="3" marker-end="url(#arrowhead)"/>
    <text x="150" y="110" class="data-text" text-anchor="middle" font-size="20" fill="#059669">+65%</text>
    
    <!-- 实施后 -->
    <rect x="190" y="60" width="60" height="120" fill="#059669" opacity="0.3"/>
    <rect x="190" y="80" width="60" height="100" fill="#059669"/>
    <text x="220" y="200" class="content-text" text-anchor="middle" font-size="18">实施后</text>
    
    <!-- 72小时标识 -->
    <circle cx="320" cy="120" r="40" fill="#2563EB" opacity="0.2"/>
    <text x="320" y="115" class="content-text" text-anchor="middle" font-size="16" fill="#2563EB">72h</text>
    <text x="320" y="135" class="content-text" text-anchor="middle" font-size="16" fill="#2563EB">激活</text>
  </g>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#059669"/>
    </marker>
  </defs>
</svg>
