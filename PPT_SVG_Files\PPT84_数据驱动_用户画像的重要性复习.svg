<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .key-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">数据驱动：用户画像的重要性（复习）</text>
  
  <!-- 前提条件 -->
  <rect x="100" y="240" width="1720" height="180" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="310" r="20" fill="#2563EB"/>
  <text x="180" y="320" class="content-text" text-anchor="middle" fill="white" font-size="20">前提</text>
  <text x="230" y="290" class="section-title" fill="#2563EB">核心前提：</text>
  <text x="230" y="340" class="key-text" fill="#2563EB">精准评估和运营的前提是了解用户</text>
  <text x="230" y="380" class="content-text">没有准确的用户画像，就无法做到精准服务</text>
  
  <!-- 基础支撑 -->
  <rect x="100" y="440" width="1720" height="180" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="510" r="20" fill="#059669"/>
  <text x="180" y="520" class="content-text" text-anchor="middle" fill="white" font-size="20">基础</text>
  <text x="230" y="490" class="section-title" fill="#059669">基础支撑：</text>
  <text x="230" y="540" class="key-text" fill="#059669">标签体系是用户画像的基础</text>
  <text x="230" y="580" class="content-text">通过多维度标签构建完整的用户画像</text>
  
  <!-- 截图展示 -->
  <rect x="100" y="640" width="1720" height="240" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="730" r="20" fill="#D97706"/>
  <text x="180" y="740" class="content-text" text-anchor="middle" fill="white" font-size="18">展示</text>
  <text x="230" y="710" class="section-title" fill="#D97706">系统展示：</text>
  <text x="230" y="760" class="content-text">截图：展示用户画像标签示例</text>
  <text x="230" y="800" class="content-text">包含基础信息、行为特征、偏好标签等</text>
  <text x="230" y="840" class="content-text">为精准营销和个性化服务提供数据支撑</text>
  
  <!-- 装饰元素 - 用户画像示意图 -->
  <g transform="translate(1400, 500)">
    <!-- 用户画像框架 -->
    <rect x="0" y="0" width="400" height="350" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="200" y="30" class="content-text" text-anchor="middle" font-size="24">用户画像标签</text>
    
    <!-- 标签分类 -->
    <g transform="translate(30, 60)">
      <!-- 基础信息 -->
      <text x="0" y="20" class="content-text" font-size="20" fill="#2563EB">基础信息：</text>
      <rect x="0" y="30" width="80" height="25" fill="#DBEAFE" rx="5"/>
      <text x="40" y="47" class="content-text" text-anchor="middle" font-size="14">年龄30-40</text>
      
      <rect x="90" y="30" width="80" height="25" fill="#DBEAFE" rx="5"/>
      <text x="130" y="47" class="content-text" text-anchor="middle" font-size="14">有孩家庭</text>
      
      <rect x="180" y="30" width="80" height="25" fill="#DBEAFE" rx="5"/>
      <text x="220" y="47" class="content-text" text-anchor="middle" font-size="14">中高收入</text>
      
      <!-- 行为特征 -->
      <text x="0" y="90" class="content-text" font-size="20" fill="#059669">行为特征：</text>
      <rect x="0" y="100" width="80" height="25" fill="#DCFCE7" rx="5"/>
      <text x="40" y="117" class="content-text" text-anchor="middle" font-size="14">高活跃度</text>
      
      <rect x="90" y="100" width="80" height="25" fill="#DCFCE7" rx="5"/>
      <text x="130" y="117" class="content-text" text-anchor="middle" font-size="14">重度用户</text>
      
      <rect x="180" y="100" width="80" height="25" fill="#DCFCE7" rx="5"/>
      <text x="220" y="117" class="content-text" text-anchor="middle" font-size="14">价格敏感</text>
      
      <!-- 偏好标签 -->
      <text x="0" y="160" class="content-text" font-size="20" fill="#D97706">偏好标签：</text>
      <rect x="0" y="170" width="80" height="25" fill="#FEF3C7" rx="5"/>
      <text x="40" y="187" class="content-text" text-anchor="middle" font-size="14">智能家居</text>
      
      <rect x="90" y="170" width="80" height="25" fill="#FEF3C7" rx="5"/>
      <text x="130" y="187" class="content-text" text-anchor="middle" font-size="14">在线教育</text>
      
      <rect x="180" y="170" width="80" height="25" fill="#FEF3C7" rx="5"/>
      <text x="220" y="187" class="content-text" text-anchor="middle" font-size="14">影音娱乐</text>
      
      <!-- 风险标签 -->
      <text x="0" y="230" class="content-text" font-size="20" fill="#DC2626">风险标签：</text>
      <rect x="0" y="240" width="80" height="25" fill="#FEE2E2" rx="5"/>
      <text x="40" y="257" class="content-text" text-anchor="middle" font-size="14">流失预警</text>
      
      <rect x="90" y="240" width="80" height="25" fill="#FEE2E2" rx="5"/>
      <text x="130" y="257" class="content-text" text-anchor="middle" font-size="14">投诉倾向</text>
    </g>
    
    <text x="200" y="330" class="content-text" text-anchor="middle" font-size="20">多维度画像</text>
  </g>
</svg>
