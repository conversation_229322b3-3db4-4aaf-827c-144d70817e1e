<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .phase-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #059669; font-weight: bold; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .tool-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2563EB; font-weight: bold; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #059669; font-style: italic; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">阶段二（24-48h）：需求激发 &amp; 体验优化</text>
  
  <!-- 动作 -->
  <rect x="100" y="220" width="1720" height="220" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="300" r="20" fill="#059669"/>
  <text x="180" y="310" class="content-text" text-anchor="middle" fill="white" font-size="20">动作</text>
  <text x="230" y="270" class="section-title" fill="#059669">关键动作：</text>
  
  <g transform="translate(280, 310)">
    <circle cx="0" cy="0" r="12" fill="#059669"/>
    <text x="30" y="8" class="content-text">推送WiFi质量诊断报告（如有问题）</text>
    
    <circle cx="0" cy="50" r="12" fill="#059669"/>
    <text x="30" y="58" class="content-text">基于初步行为推荐场景产品</text>
    
    <circle cx="0" cy="100" r="12" fill="#059669"/>
    <text x="30" y="108" class="content-text">如游戏加速、Mesh组网等</text>
    
    <circle cx="0" cy="150" r="12" fill="#059669"/>
    <text x="30" y="158" class="content-text">个性化体验优化建议</text>
  </g>
  
  <!-- 工具 -->
  <rect x="100" y="460" width="1720" height="140" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="510" r="20" fill="#2563EB"/>
  <text x="180" y="520" class="content-text" text-anchor="middle" fill="white" font-size="20">工具</text>
  <text x="230" y="490" class="section-title" fill="#2563EB">核心工具：</text>
  <text x="230" y="530" class="tool-text">WiFi远程诊断 + 推荐引擎（初级）</text>
  <text x="230" y="570" class="content-text">智能分析用户行为，精准推荐产品</text>
  
  <!-- 消息模板示例 -->
  <rect x="100" y="620" width="1720" height="240" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="710" r="20" fill="#D97706"/>
  <text x="180" y="720" class="content-text" text-anchor="middle" fill="white" font-size="18">示例</text>
  <text x="230" y="690" class="section-title" fill="#D97706">消息模板示例：</text>
  
  <g transform="translate(280, 730)">
    <rect x="0" y="0" width="1200" height="40" fill="#FEF3C7" rx="5"/>
    <text x="20" y="25" class="example-text">诊断报告："您的WiFi在卧室信号较弱，建议优化位置或升级Mesh..."</text>
    
    <rect x="0" y="50" width="1200" height="40" fill="#FEF3C7" rx="5"/>
    <text x="20" y="75" class="example-text">场景推荐："检测到您经常玩游戏，推荐游戏加速包，首月5折..."</text>
    
    <rect x="0" y="100" width="1200" height="40" fill="#FEF3C7" rx="5"/>
    <text x="20" y="125" class="example-text">Mesh推荐："大户型WiFi覆盖专家，全屋无死角，限时优惠..."</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 400)">
    <!-- 24-48小时时钟 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#059669" stroke-width="3"/>
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="24" fill="#059669">24-48h</text>
    <text x="0" y="15" class="content-text" text-anchor="middle" font-size="20" fill="#059669">第二阶段</text>
    
    <!-- 时钟指针 -->
    <line x1="0" y1="0" x2="0" y2="-50" stroke="#059669" stroke-width="3"/>
    <line x1="0" y1="0" x2="35" y2="0" stroke="#059669" stroke-width="2"/>
    <circle cx="0" cy="0" r="6" fill="#059669"/>
    
    <!-- 需求激发图标 -->
    <g transform="translate(0, 120)">
      <rect x="-40" y="-20" width="80" height="40" fill="#059669" opacity="0.2" rx="20"/>
      <text x="0" y="5" class="content-text" text-anchor="middle" font-size="18" fill="#059669">需求激发</text>
    </g>
  </g>
</svg>
