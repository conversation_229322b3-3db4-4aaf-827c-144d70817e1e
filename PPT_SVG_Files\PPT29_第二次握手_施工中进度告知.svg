<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #059669; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .tool-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #2563EB; font-weight: bold; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #059669; font-style: italic; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">第二次握手：施工中（进度告知）</text>
  
  <!-- 时机 -->
  <rect x="100" y="220" width="1720" height="140" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="270" r="15" fill="#059669"/>
  <text x="220" y="250" class="section-title">时机：</text>
  <text x="220" y="290" class="content-text">主要设备安装布线完成，开始调测时</text>
  <text x="220" y="330" class="content-text">让客户了解进度，安心等待</text>
  
  <!-- 工具 -->
  <rect x="100" y="380" width="1720" height="120" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="420" r="15" fill="#2563EB"/>
  <text x="220" y="410" class="section-title">工具：</text>
  <text x="220" y="450" class="tool-text">口头告知 / （可选）平台实时播报</text>
  <text x="220" y="490" class="content-text">简单直接，及时沟通</text>
  
  <!-- 内容 -->
  <rect x="100" y="520" width="1720" height="180" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="580" r="15" fill="#D97706"/>
  <text x="220" y="560" class="section-title">告知内容：</text>
  <text x="270" y="600" class="content-text">• 告知已完成的工作</text>
  <text x="270" y="640" class="content-text">• 说明下一步工作内容</text>
  <text x="270" y="680" class="content-text">• 预计剩余完成时间</text>
  
  <!-- 话术 -->
  <rect x="100" y="720" width="1720" height="180" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="780" r="15" fill="#DC2626"/>
  <text x="220" y="760" class="section-title">标准话术：</text>
  <text x="220" y="800" class="speech-text">"王先生，设备和线都弄好了，现在开始配置网络，</text>
  <text x="220" y="840" class="speech-text">大概还需20分钟。您可以先忙其他事，马上就好。"</text>
  
  <!-- 装饰元素 - 进度条 -->
  <g transform="translate(1400, 350)">
    <!-- 进度条背景 -->
    <rect x="0" y="0" width="400" height="300" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="200" y="40" class="content-text" text-anchor="middle" font-size="24">施工进度</text>
    
    <!-- 进度步骤 -->
    <g transform="translate(50, 80)">
      <!-- 已完成 -->
      <circle cx="0" cy="0" r="15" fill="#059669"/>
      <text x="30" y="8" class="content-text" font-size="20">设备安装 ✓</text>
      
      <circle cx="0" cy="50" r="15" fill="#059669"/>
      <text x="30" y="58" class="content-text" font-size="20">线路布设 ✓</text>
      
      <!-- 进行中 -->
      <circle cx="0" cy="100" r="15" fill="#D97706"/>
      <text x="30" y="108" class="content-text" font-size="20">网络配置 ⏳</text>
      
      <!-- 待完成 -->
      <circle cx="0" cy="150" r="15" fill="#6B7280"/>
      <text x="30" y="158" class="content-text" font-size="20">测试验收</text>
      
      <!-- 进度条 -->
      <rect x="0" y="180" width="300" height="20" fill="#E5E7EB" rx="10"/>
      <rect x="0" y="180" width="180" height="20" fill="#059669" rx="10"/>
      <text x="150" y="225" class="content-text" text-anchor="middle" font-size="18">60% 完成</text>
    </g>
  </g>
</svg>
