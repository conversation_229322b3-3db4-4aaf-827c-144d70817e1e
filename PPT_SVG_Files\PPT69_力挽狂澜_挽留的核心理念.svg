<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #374151; }
      .goal-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 38px; font-weight: bold; }
      .attitude-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="220" class="title-text">力挽狂澜：挽留的核心理念</text>
  
  <!-- 目标 -->
  <rect x="150" y="280" width="1620" height="200" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="30"/>
  <circle cx="220" cy="350" r="20" fill="#2563EB"/>
  <text x="220" y="360" class="content-text" text-anchor="middle" fill="white" font-size="20">目标</text>
  <text x="270" y="330" class="section-title" fill="#2563EB">挽留目标：</text>
  <text x="270" y="380" class="goal-text" fill="#2563EB">不仅留住人，更要留住心</text>
  <text x="270" y="430" class="goal-text" fill="#2563EB">甚至创造新价值</text>
  <text x="270" y="470" class="content-text">化危机为转机，变流失为忠诚</text>
  
  <!-- 心态 -->
  <rect x="150" y="500" width="1620" height="280" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="30"/>
  <circle cx="220" cy="600" r="20" fill="#059669"/>
  <text x="220" y="610" class="content-text" text-anchor="middle" fill="white" font-size="20">心态</text>
  <text x="270" y="580" class="section-title" fill="#059669">服务心态：</text>
  
  <g transform="translate(320, 620)">
    <circle cx="0" cy="0" r="15" fill="#059669"/>
    <text x="30" y="8" class="attitude-text" fill="#059669">真诚</text>
    <text x="120" y="8" class="content-text">发自内心的关怀和歉意</text>
    
    <circle cx="0" cy="60" r="15" fill="#059669"/>
    <text x="30" y="68" class="attitude-text" fill="#059669">专业</text>
    <text x="120" y="68" class="content-text">用专业能力解决问题</text>
    
    <circle cx="0" cy="120" r="15" fill="#059669"/>
    <text x="30" y="128" class="attitude-text" fill="#059669">灵活</text>
    <text x="120" y="128" class="content-text">因人而异，个性化方案</text>
    
    <circle cx="0" cy="180" r="15" fill="#059669"/>
    <text x="30" y="188" class="attitude-text" fill="#059669">不放弃</text>
    <text x="120" y="188" class="content-text">坚持到底，直到满意</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1500, 550)">
    <!-- 挽留之手 -->
    <circle cx="0" cy="0" r="120" fill="none" stroke="#DC2626" stroke-width="4" opacity="0.3"/>
    <circle cx="0" cy="0" r="80" fill="#DC2626" opacity="0.1"/>
    
    <!-- 心形图标 -->
    <path d="M 0 20 C -20 0 -40 0 -40 -20 C -40 -40 -20 -40 0 -20 C 20 -40 40 -40 40 -20 C 40 0 20 0 0 20 Z" 
          fill="#DC2626" opacity="0.6"/>
    
    <text x="0" y="160" class="content-text" text-anchor="middle" font-size="24" fill="#DC2626">用心挽留</text>
  </g>
  
  <!-- 底部强调 -->
  <rect x="200" y="800" width="1520" height="120" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="25"/>
  <text x="960" y="840" class="goal-text" text-anchor="middle" fill="#DC2626">挽留的最高境界：</text>
  <text x="960" y="880" class="content-text" text-anchor="middle">让客户因为这次经历而更加信任我们</text>
  <text x="960" y="910" class="content-text" text-anchor="middle">变投诉为口碑，变流失为推荐</text>
</svg>
