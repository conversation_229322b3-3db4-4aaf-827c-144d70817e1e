<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #059669; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #DC2626; font-weight: bold; }
      .kpi-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #2563EB; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">战略要求：公司要我们怎么做？</text>
  
  <!-- 智慧家庭3.0 -->
  <rect x="100" y="240" width="1720" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="310" r="20" fill="#059669"/>
  <text x="180" y="320" class="content-text" text-anchor="middle" fill="white" font-size="24">3.0</text>
  <text x="230" y="290" class="section-title">智慧家庭3.0战略</text>
  <text x="230" y="340" class="content-text">"前装"是基础，装不好，后面智能家居、应用都是空谈</text>
  <text x="230" y="390" class="highlight-text">入网质量是智慧家庭服务的第一道关口</text>
  
  <!-- KPI硬指标 -->
  <rect x="100" y="460" width="1720" height="200" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="530" r="20" fill="#2563EB"/>
  <text x="180" y="540" class="content-text" text-anchor="middle" fill="white" font-size="20">KPI</text>
  <text x="230" y="510" class="section-title">KPI硬指标</text>
  <text x="230" y="560" class="kpi-text">装维满意度 ≥ 90%（或92分）</text>
  <text x="230" y="610" class="highlight-text">入网是第一关，必须做到位！</text>
  
  <!-- 核心要求 -->
  <rect x="100" y="680" width="1720" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="750" r="20" fill="#D97706"/>
  <text x="180" y="760" class="content-text" text-anchor="middle" fill="white" font-size="18">核心</text>
  <text x="230" y="730" class="section-title">核心要求</text>
  <text x="230" y="780" class="content-text">提供高品质、标准化的安装服务</text>
  <text x="230" y="830" class="highlight-text">让每一次入网都成为客户满意的开始</text>
  
  <!-- 装饰元素 -->
  <path d="M 1500 350 Q 1600 300 1700 350" class="accent-curve" opacity="0.6"/>
  <path d="M 1500 570 Q 1600 520 1700 570" class="accent-curve" opacity="0.6"/>
  <path d="M 1500 790 Q 1600 740 1700 790" class="accent-curve" opacity="0.6"/>
</svg>
