<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #059669; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .method-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="150" class="title-text">我们的目标：带走什么？</text>
  
  <!-- 认知提升 -->
  <rect x="80" y="200" width="1760" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="150" cy="250" r="15" fill="#059669"/>
  <text x="200" y="270" class="section-title">认知提升</text>
  <text x="200" y="320" class="content-text">知道每个环节做好/做差的后果，理解客户体验关键点</text>
  
  <!-- 技能强化 -->
  <rect x="80" y="420" width="1760" height="280" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="150" cy="470" r="15" fill="#2563EB"/>
  <text x="200" y="490" class="section-title">技能强化</text>
  <text x="200" y="540" class="content-text">学会用标准流程、方法、工具解决实际问题</text>
  <text x="200" y="590" class="method-text">核心方法：</text>
  <text x="200" y="630" class="content-text">"装前六必查"、"三次握手"、"黄金72小时SOP"、</text>
  <text x="200" y="670" class="content-text">"三阶八步挽留法"等</text>
  
  <!-- 工具应用 -->
  <rect x="80" y="720" width="1760" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="150" cy="770" r="15" fill="#D97706"/>
  <text x="200" y="790" class="section-title">工具应用</text>
  <text x="200" y="840" class="content-text">熟练操作"爱家平台"核心功能</text>
  <text x="200" y="880" class="content-text">（预检、诊断、热力图、推荐等）</text>
  
  <!-- 装饰元素 -->
  <path d="M 1600 300 Q 1700 250 1800 300" class="accent-curve" opacity="0.6"/>
  <path d="M 1600 550 Q 1700 500 1800 550" class="accent-curve" opacity="0.6"/>
  <path d="M 1600 800 Q 1700 750 1800 800" class="accent-curve" opacity="0.6"/>
</svg>
