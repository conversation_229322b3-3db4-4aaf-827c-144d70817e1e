<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #8B5CF6; text-anchor: middle; }
      .module-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">两天学习回顾</text>
  <text x="960" y="240" class="subtitle-text">（知识树/思维导图）</text>
  
  <!-- 中心主题 -->
  <circle cx="960" cy="540" r="80" fill="#1E3A8A" opacity="0.8"/>
  <text x="960" y="530" class="content-text" text-anchor="middle" fill="white" font-size="20">四川移动</text>
  <text x="960" y="555" class="content-text" text-anchor="middle" fill="white" font-size="20">家宽运营</text>
  
  <!-- 模块一：入网质量筑基 -->
  <g transform="translate(400, 350)">
    <circle cx="0" cy="0" r="60" fill="#059669" opacity="0.8"/>
    <text x="0" y="-5" class="module-text" text-anchor="middle" fill="white" font-size="18">模块一</text>
    <text x="0" y="15" class="module-text" text-anchor="middle" fill="white" font-size="16">入网筑基</text>
    
    <!-- 连接线 -->
    <line x1="60" y1="0" x2="420" y2="150" stroke="#059669" stroke-width="3"/>
    
    <!-- 子要点 -->
    <g transform="translate(-120, -80)">
      <text x="0" y="0" class="content-text" font-size="18" fill="#059669">预检流程</text>
      <text x="0" y="25" class="content-text" font-size="18" fill="#059669">工具应用</text>
      <text x="0" y="50" class="content-text" font-size="18" fill="#059669">质量标准</text>
    </g>
  </g>
  
  <!-- 模块二：激活与活性管理 -->
  <g transform="translate(1520, 350)">
    <circle cx="0" cy="0" r="60" fill="#2563EB" opacity="0.8"/>
    <text x="0" y="-5" class="module-text" text-anchor="middle" fill="white" font-size="18">模块二</text>
    <text x="0" y="15" class="module-text" text-anchor="middle" fill="white" font-size="16">激活管理</text>
    
    <!-- 连接线 -->
    <line x1="-60" y1="0" x2="-420" y2="150" stroke="#2563EB" stroke-width="3"/>
    
    <!-- 子要点 -->
    <g transform="translate(80, -80)">
      <text x="0" y="0" class="content-text" font-size="18" fill="#2563EB">72小时</text>
      <text x="0" y="25" class="content-text" font-size="18" fill="#2563EB">WiFi优化</text>
      <text x="0" y="50" class="content-text" font-size="18" fill="#2563EB">活跃度</text>
    </g>
  </g>
  
  <!-- 模块三：异常处置与挽留 -->
  <g transform="translate(400, 730)">
    <circle cx="0" cy="0" r="60" fill="#DC2626" opacity="0.8"/>
    <text x="0" y="-5" class="module-text" text-anchor="middle" fill="white" font-size="18">模块三</text>
    <text x="0" y="15" class="module-text" text-anchor="middle" fill="white" font-size="16">异常挽留</text>
    
    <!-- 连接线 -->
    <line x1="60" y1="0" x2="420" y2="-150" stroke="#DC2626" stroke-width="3"/>
    
    <!-- 子要点 -->
    <g transform="translate(-120, 80)">
      <text x="0" y="0" class="content-text" font-size="18" fill="#DC2626">预警识别</text>
      <text x="0" y="25" class="content-text" font-size="18" fill="#DC2626">八步挽留</text>
      <text x="0" y="50" class="content-text" font-size="18" fill="#DC2626">工具应用</text>
    </g>
  </g>
  
  <!-- 模块四：价值深耕实战 -->
  <g transform="translate(1520, 730)">
    <circle cx="0" cy="0" r="60" fill="#8B5CF6" opacity="0.8"/>
    <text x="0" y="-5" class="module-text" text-anchor="middle" fill="white" font-size="18">模块四</text>
    <text x="0" y="15" class="module-text" text-anchor="middle" fill="white" font-size="16">价值深耕</text>
    
    <!-- 连接线 -->
    <line x1="-60" y1="0" x2="-420" y2="-150" stroke="#8B5CF6" stroke-width="3"/>
    
    <!-- 子要点 -->
    <g transform="translate(80, 80)">
      <text x="0" y="0" class="content-text" font-size="18" fill="#8B5CF6">CLV理念</text>
      <text x="0" y="25" class="content-text" font-size="18" fill="#8B5CF6">场景营销</text>
      <text x="0" y="50" class="content-text" font-size="18" fill="#8B5CF6">网格运营</text>
    </g>
  </g>
  
  <!-- 模块五：综合实战演练 -->
  <g transform="translate(960, 800)">
    <circle cx="0" cy="0" r="50" fill="#D97706" opacity="0.8"/>
    <text x="0" y="-5" class="module-text" text-anchor="middle" fill="white" font-size="16">模块五</text>
    <text x="0" y="12" class="module-text" text-anchor="middle" fill="white" font-size="14">实战演练</text>
    
    <!-- 连接线 -->
    <line x1="0" y1="-50" x2="0" y2="-180" stroke="#D97706" stroke-width="3"/>
  </g>
  
  <!-- 底部说明 -->
  <text x="960" y="320" class="content-text" text-anchor="middle" fill="#6B7280">用一张图串联两天所有核心知识点、方法、工具</text>
  <text x="960" y="350" class="content-text" text-anchor="middle" fill="#6B7280">快速回顾，加深印象</text>
</svg>
