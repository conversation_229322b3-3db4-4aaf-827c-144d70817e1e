<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .key-point { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="220" class="title-text">模块五总结</text>
  
  <!-- 要点1 -->
  <rect x="150" y="280" width="1620" height="160" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="30"/>
  <circle cx="220" cy="340" r="20" fill="#059669"/>
  <text x="220" y="350" class="content-text" text-anchor="middle" fill="white" font-size="24">1</text>
  <text x="270" y="350" class="key-point" fill="#059669">学习标杆，启发思路</text>
  <text x="270" y="400" class="content-text">借鉴浙江模式成功经验，结合实际创新应用</text>
  
  <!-- 要点2 -->
  <rect x="150" y="460" width="1620" height="160" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="30"/>
  <circle cx="220" cy="520" r="20" fill="#2563EB"/>
  <text x="220" y="530" class="content-text" text-anchor="middle" fill="white" font-size="24">2</text>
  <text x="270" y="530" class="key-point" fill="#2563EB">诊断自身，明确方向</text>
  <text x="270" y="580" class="content-text">深入分析问题根源，制定针对性改进措施</text>
  
  <!-- 要点3 -->
  <rect x="150" y="640" width="1620" height="160" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="30"/>
  <circle cx="220" cy="700" r="20" fill="#D97706"/>
  <text x="220" y="710" class="content-text" text-anchor="middle" fill="white" font-size="24">3</text>
  <text x="270" y="710" class="key-point" fill="#D97706">制定计划，付诸行动</text>
  <text x="270" y="760" class="content-text">90天改进计划落地执行，持续跟踪优化</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 550)">
    <circle cx="0" cy="0" r="80" fill="none" stroke="#3B82F6" stroke-width="4" opacity="0.3"/>
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="28">模块五</text>
    <text x="0" y="20" class="content-text" text-anchor="middle" font-size="28">完成</text>
    <circle cx="0" cy="0" r="50" fill="#DC2626" opacity="0.2"/>
    
    <!-- 实战图标 -->
    <g transform="translate(0, 120)">
      <circle cx="0" cy="0" r="40" fill="none" stroke="#DC2626" stroke-width="3"/>
      <path d="M -15 0 L -5 10 L 15 -10" stroke="#DC2626" stroke-width="4" fill="none"/>
      <text x="0" y="60" class="content-text" text-anchor="middle" font-size="18" fill="#DC2626">实战演练</text>
    </g>
  </g>
</svg>
