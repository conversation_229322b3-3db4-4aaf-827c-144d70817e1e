<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .app-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #8B5CF6; font-weight: bold; }
      .action-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #059669; }
      .benefit-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">活性提升方法：互动与积分体系（简介）</text>
  
  <!-- 思路 -->
  <rect x="100" y="220" width="1720" height="160" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="280" r="20" fill="#8B5CF6"/>
  <text x="180" y="290" class="content-text" text-anchor="middle" fill="white" font-size="20">思路</text>
  <text x="230" y="260" class="section-title" fill="#8B5CF6">核心思路：</text>
  <text x="230" y="310" class="content-text">游戏化、社交化提升用户参与感</text>
  <text x="230" y="350" class="content-text">让用户在互动中感知价值，增强粘性</text>
  
  <!-- 一线可做 -->
  <rect x="100" y="400" width="1720" height="280" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="480" r="20" fill="#059669"/>
  <text x="180" y="490" class="content-text" text-anchor="middle" fill="white" font-size="18">实践</text>
  <text x="230" y="460" class="section-title" fill="#059669">一线可做：</text>
  
  <g transform="translate(280, 500)">
    <circle cx="0" cy="0" r="12" fill="#059669"/>
    <text x="30" y="8" class="action-text">引导用户下载/使用APP：</text>
    
    <g transform="translate(50, 40)">
      <rect x="0" y="0" width="150" height="40" fill="#DBEAFE" rx="5"/>
      <text x="75" y="25" class="app-text" text-anchor="middle">爱家APP</text>
      
      <rect x="170" y="0" width="150" height="40" fill="#DCFCE7" rx="5"/>
      <text x="245" y="25" class="app-text" text-anchor="middle">和家亲APP</text>
    </g>
    
    <circle cx="0" cy="100" r="12" fill="#059669"/>
    <text x="30" y="108" class="action-text">告知用户积分获取方式：</text>
    <text x="50" y="138" class="content-text">签到/做任务可以得积分换礼品</text>
    
    <circle cx="0" cy="180" r="12" fill="#059669"/>
    <text x="30" y="188" class="action-text">在服务过程中主动引导参与互动活动</text>
  </g>
  
  <!-- 积分体系示例 -->
  <rect x="100" y="700" width="1720" height="240" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="790" r="20" fill="#D97706"/>
  <text x="180" y="800" class="content-text" text-anchor="middle" fill="white" font-size="18">体系</text>
  <text x="230" y="770" class="section-title" fill="#D97706">积分体系示例：</text>
  
  <g transform="translate(280, 810)">
    <text x="0" y="0" class="content-text">• 每日签到：+10积分</text>
    <text x="300" y="0" class="content-text">• 完成任务：+50积分</text>
    <text x="600" y="0" class="content-text">• 邀请好友：+100积分</text>
    
    <text x="0" y="40" class="content-text">• 参与活动：+20积分</text>
    <text x="300" y="40" class="content-text">• 评价服务：+30积分</text>
    <text x="600" y="40" class="content-text">• 分享推荐：+80积分</text>
    
    <text x="0" y="100" class="benefit-text">积分兑换：话费券、流量包、智能设备优惠券等</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <!-- 积分互动图 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#8B5CF6" stroke-width="3" opacity="0.3"/>
    
    <!-- 积分图标 -->
    <circle cx="0" cy="0" r="40" fill="#D97706" opacity="0.2"/>
    <text x="0" y="-5" class="content-text" text-anchor="middle" font-size="24" fill="#D97706">积分</text>
    <text x="0" y="20" class="content-text" text-anchor="middle" font-size="20" fill="#D97706">系统</text>
    
    <!-- 互动元素 -->
    <circle cx="-60" cy="-60" r="15" fill="#059669" opacity="0.6"/>
    <text x="-60" y="-55" class="content-text" text-anchor="middle" fill="white" font-size="12">签到</text>
    
    <circle cx="60" cy="-60" r="15" fill="#2563EB" opacity="0.6"/>
    <text x="60" y="-55" class="content-text" text-anchor="middle" fill="white" font-size="12">任务</text>
    
    <circle cx="-60" cy="60" r="15" fill="#DC2626" opacity="0.6"/>
    <text x="-60" y="65" class="content-text" text-anchor="middle" fill="white" font-size="12">分享</text>
    
    <circle cx="60" cy="60" r="15" fill="#8B5CF6" opacity="0.6"/>
    <text x="60" y="65" class="content-text" text-anchor="middle" fill="white" font-size="12">兑换</text>
    
    <text x="0" y="120" class="content-text" text-anchor="middle" font-size="20">互动体系</text>
  </g>
</svg>
