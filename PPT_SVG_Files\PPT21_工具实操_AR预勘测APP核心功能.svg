<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #059669; font-weight: bold; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #374151; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #374151; }
      .value-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">工具实操：AR预勘测APP核心功能</text>
  
  <!-- 价值说明 -->
  <rect x="100" y="200" width="1720" height="100" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="15"/>
  <text x="150" y="240" class="value-text">价值：</text>
  <text x="150" y="280" class="content-text">提前规划WiFi覆盖，避免后期扯皮，提升客户满意度</text>
  
  <!-- 核心步骤 -->
  <text x="150" y="350" class="section-title">核心操作步骤（图文/截图）：</text>
  
  <!-- 步骤列表 -->
  <g transform="translate(150, 380)">
    <!-- 步骤1 -->
    <circle cx="0" cy="20" r="15" fill="#DC2626"/>
    <text x="0" y="28" class="content-text" text-anchor="middle" fill="white" font-size="18">1</text>
    <text x="40" y="28" class="step-text">创建户型</text>
    <text x="200" y="28" class="content-text">（扫描/绘制房屋结构）</text>
    
    <!-- 步骤2 -->
    <circle cx="0" cy="80" r="15" fill="#059669"/>
    <text x="0" y="88" class="content-text" text-anchor="middle" fill="white" font-size="18">2</text>
    <text x="40" y="88" class="step-text">设置墙体材质</text>
    <text x="200" y="88" class="content-text">（混凝土、木质、玻璃等）</text>
    
    <!-- 步骤3 -->
    <circle cx="0" cy="140" r="15" fill="#2563EB"/>
    <text x="0" y="148" class="content-text" text-anchor="middle" fill="white" font-size="18">3</text>
    <text x="40" y="148" class="step-text">模拟放置AP</text>
    <text x="200" y="148" class="content-text">（路由器/面板AP位置）</text>
    
    <!-- 步骤4 -->
    <circle cx="0" cy="200" r="15" fill="#D97706"/>
    <text x="0" y="208" class="content-text" text-anchor="middle" fill="white" font-size="18">4</text>
    <text x="40" y="208" class="step-text">查看实时热力图</text>
    <text x="200" y="208" class="content-text">（信号强度分布）</text>
    
    <!-- 步骤5 -->
    <circle cx="0" cy="260" r="15" fill="#8B5CF6"/>
    <text x="0" y="268" class="content-text" text-anchor="middle" fill="white" font-size="18">5</text>
    <text x="40" y="268" class="step-text">生成报告与客户确认</text>
    <text x="200" y="268" class="content-text">（方案对比展示）</text>
  </g>
  
  <!-- 热力图效果展示 -->
  <rect x="1000" y="380" width="720" height="400" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
  <text x="1360" y="420" class="section-title" text-anchor="middle">热力图效果对比</text>
  
  <!-- 优化前 -->
  <rect x="1050" y="450" width="300" height="150" fill="#FEE2E2" stroke="#DC2626" stroke-width="2" rx="10"/>
  <text x="1200" y="480" class="content-text" text-anchor="middle">优化前</text>
  <text x="1200" y="510" class="content-text" text-anchor="middle" font-size="20">信号覆盖不均</text>
  <circle cx="1200" cy="550" r="30" fill="#DC2626" opacity="0.5"/>
  
  <!-- 优化后 -->
  <rect x="1370" y="450" width="300" height="150" fill="#DCFCE7" stroke="#059669" stroke-width="2" rx="10"/>
  <text x="1520" y="480" class="content-text" text-anchor="middle">优化后</text>
  <text x="1520" y="510" class="content-text" text-anchor="middle" font-size="20">全屋绿色覆盖</text>
  <circle cx="1520" cy="550" r="30" fill="#059669" opacity="0.5"/>
  
  <!-- 箭头 -->
  <path d="M 1360 525 L 1400 525" stroke="#6B7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6B7280"/>
    </marker>
  </defs>
</svg>
