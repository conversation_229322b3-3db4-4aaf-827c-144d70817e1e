<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .app-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2563EB; font-weight: bold; }
      .action-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #059669; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">活性提升方法：内容与应用引入（简介）</text>
  
  <!-- 思路 -->
  <rect x="100" y="220" width="1720" height="160" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="280" r="20" fill="#2563EB"/>
  <text x="180" y="290" class="content-text" text-anchor="middle" fill="white" font-size="20">思路</text>
  <text x="230" y="260" class="section-title" fill="#2563EB">核心思路：</text>
  <text x="230" y="310" class="content-text">让宽带承载更多应用，增加用户粘性</text>
  <text x="230" y="350" class="content-text">从单纯的网络接入升级为智慧生活平台</text>
  
  <!-- 一线可做 -->
  <rect x="100" y="400" width="1720" height="300" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="480" r="20" fill="#059669"/>
  <text x="180" y="490" class="content-text" text-anchor="middle" fill="white" font-size="18">实践</text>
  <text x="230" y="460" class="section-title" fill="#059669">一线可做：</text>
  
  <g transform="translate(280, 500)">
    <circle cx="0" cy="0" r="12" fill="#059669"/>
    <text x="30" y="8" class="action-text">了解并能简单介绍核心应用：</text>
    
    <g transform="translate(50, 40)">
      <rect x="0" y="0" width="120" height="40" fill="#DBEAFE" rx="5"/>
      <text x="60" y="25" class="app-text" text-anchor="middle">家庭云</text>
      
      <rect x="140" y="0" width="120" height="40" fill="#DCFCE7" rx="5"/>
      <text x="200" y="25" class="app-text" text-anchor="middle">移动看家</text>
      
      <rect x="280" y="0" width="120" height="40" fill="#FEF3C7" rx="5"/>
      <text x="340" y="25" class="app-text" text-anchor="middle">和家亲</text>
      
      <rect x="420" y="0" width="120" height="40" fill="#FEE2E2" rx="5"/>
      <text x="480" y="25" class="app-text" text-anchor="middle">智能家居</text>
    </g>
    
    <circle cx="0" cy="100" r="12" fill="#059669"/>
    <text x="30" y="108" class="action-text">在服务过程中，根据用户需求随口推荐</text>
    
    <circle cx="0" cy="150" r="12" fill="#059669"/>
    <text x="30" y="158" class="action-text">重点关注用户生活场景，精准匹配应用</text>
  </g>
  
  <!-- 应用场景示例 -->
  <rect x="100" y="720" width="1720" height="240" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="810" r="20" fill="#D97706"/>
  <text x="180" y="820" class="content-text" text-anchor="middle" fill="white" font-size="18">场景</text>
  <text x="230" y="780" class="section-title" fill="#D97706">推荐场景示例：</text>
  
  <g transform="translate(280, 820)">
    <text x="0" y="0" class="content-text">• 有老人小孩的家庭 → 推荐"移动看家"远程监护</text>
    <text x="0" y="40" class="content-text">• 经常出差的用户 → 推荐"家庭云"文件同步</text>
    <text x="0" y="80" class="content-text">• 智能设备较多 → 推荐"和家亲"统一管理</text>
    <text x="0" y="120" class="content-text">• 年轻家庭 → 推荐智能家居套餐</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <!-- 应用生态图 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#2563EB" stroke-width="3" opacity="0.3"/>
    <circle cx="0" cy="0" r="50" fill="#2563EB" opacity="0.1"/>
    
    <!-- 应用图标 -->
    <rect x="-60" y="-60" width="40" height="40" fill="#059669" opacity="0.6" rx="5"/>
    <rect x="20" y="-60" width="40" height="40" fill="#D97706" opacity="0.6" rx="5"/>
    <rect x="-60" y="20" width="40" height="40" fill="#DC2626" opacity="0.6" rx="5"/>
    <rect x="20" y="20" width="40" height="40" fill="#8B5CF6" opacity="0.6" rx="5"/>
    
    <text x="0" y="5" class="content-text" text-anchor="middle" font-size="20" fill="#2563EB">宽带</text>
    <text x="0" y="120" class="content-text" text-anchor="middle" font-size="20">应用生态</text>
  </g>
</svg>
