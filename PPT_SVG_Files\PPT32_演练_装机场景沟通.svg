<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .scenario-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #DC2626; font-weight: bold; }
      .task-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #059669; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">演练：装机场景沟通</text>
  
  <!-- 场景设定 -->
  <rect x="100" y="240" width="1720" height="200" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="310" r="20" fill="#DC2626"/>
  <text x="180" y="320" class="content-text" text-anchor="middle" fill="white" font-size="24">场景</text>
  <text x="230" y="290" class="section-title" fill="#DC2626">演练场景：</text>
  <text x="230" y="340" class="scenario-text">客户家光功率预警（-28dBm）</text>
  <text x="230" y="380" class="scenario-text">且对WiFi覆盖期望很高</text>
  <text x="230" y="420" class="content-text">需要妥善处理技术问题和客户期望</text>
  
  <!-- 任务要求 -->
  <rect x="100" y="460" width="1720" height="280" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="530" r="20" fill="#059669"/>
  <text x="180" y="540" class="content-text" text-anchor="middle" fill="white" font-size="24">任务</text>
  <text x="230" y="510" class="section-title" fill="#059669">演练任务：</text>
  <text x="230" y="560" class="content-text">分组讨论并演练如何运用以下方法进行沟通：</text>
  <text x="280" y="610" class="task-text">• "六必查"标准流程</text>
  <text x="280" y="650" class="task-text">• "AR预勘"工具展示</text>
  <text x="280" y="690" class="task-text">• "三次握手"服务确认</text>
  <text x="280" y="730" class="task-text">• 相应话术和沟通技巧</text>
  
  <!-- 演练要求 -->
  <rect x="100" y="760" width="1720" height="200" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="830" r="20" fill="#2563EB"/>
  <text x="180" y="840" class="content-text" text-anchor="middle" fill="white" font-size="24">要求</text>
  <text x="230" y="810" class="section-title" fill="#2563EB">演练要求：</text>
  <text x="280" y="860" class="content-text">• 每组15分钟讨论，5分钟展示</text>
  <text x="280" y="900" class="content-text">• 重点展示沟通话术和处理技巧</text>
  <text x="280" y="940" class="content-text">• 体现专业性和客户关怀</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1500, 400)">
    <!-- 演练示意图 -->
    <circle cx="0" cy="0" r="100" fill="none" stroke="#3B82F6" stroke-width="4" opacity="0.3"/>
    <circle cx="-60" cy="-60" r="40" fill="#DC2626" opacity="0.6"/>
    <circle cx="60" cy="-60" r="40" fill="#059669" opacity="0.6"/>
    <circle cx="0" cy="80" r="40" fill="#2563EB" opacity="0.6"/>
    
    <text x="0" y="-140" class="content-text" text-anchor="middle" font-size="20">分组演练</text>
    <text x="-60" y="-50" class="content-text" text-anchor="middle" fill="white" font-size="16">A组</text>
    <text x="60" y="-50" class="content-text" text-anchor="middle" fill="white" font-size="16">B组</text>
    <text x="0" y="90" class="content-text" text-anchor="middle" fill="white" font-size="16">C组</text>
  </g>
</svg>
