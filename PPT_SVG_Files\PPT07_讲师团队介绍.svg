<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .role-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #059669; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">讲师团队介绍</text>
  
  <!-- 主讲师区域 -->
  <rect x="150" y="250" width="700" height="400" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  
  <!-- 主讲师头像占位 -->
  <circle cx="300" cy="380" r="80" fill="#E5E7EB" stroke="#059669" stroke-width="3"/>
  <text x="300" y="390" class="content-text" text-anchor="middle" font-size="24">主讲师</text>
  <text x="300" y="410" class="content-text" text-anchor="middle" font-size="24">照片</text>
  
  <!-- 主讲师信息 -->
  <text x="450" y="320" class="role-text">主讲师</text>
  <text x="450" y="370" class="content-text">• 10年+家宽运营实战经验</text>
  <text x="450" y="410" class="content-text">• 曾负责省级家宽质量提升项目</text>
  <text x="450" y="450" class="content-text">• 专注客户体验优化与流程改进</text>
  <text x="450" y="490" class="content-text">• 培训学员超过5000人次</text>
  
  <!-- 技术顾问区域 -->
  <rect x="1070" y="250" width="700" height="400" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  
  <!-- 技术顾问头像占位 -->
  <circle cx="1220" cy="380" r="80" fill="#E5E7EB" stroke="#2563EB" stroke-width="3"/>
  <text x="1220" y="390" class="content-text" text-anchor="middle" font-size="24">技术顾问</text>
  <text x="1220" y="410" class="content-text" text-anchor="middle" font-size="24">照片</text>
  
  <!-- 技术顾问信息 -->
  <text x="1370" y="320" class="role-text">技术顾问</text>
  <text x="1370" y="370" class="content-text">• 网络技术专家</text>
  <text x="1370" y="410" class="content-text">• 智慧家庭解决方案架构师</text>
  <text x="1370" y="450" class="content-text">• 爱家平台核心开发团队成员</text>
  <text x="1370" y="490" class="content-text">• 多项技术专利持有者</text>
  
  <!-- 底部说明 -->
  <rect x="400" y="720" width="1120" height="120" fill="#FEF3C7" stroke="#D97706" stroke-width="2" rx="15"/>
  <text x="960" y="760" class="content-text" text-anchor="middle">团队优势：理论与实践相结合</text>
  <text x="960" y="800" class="content-text" text-anchor="middle">既有丰富的一线经验，又有深厚的技术功底</text>
  
  <!-- 装饰元素 -->
  <path d="M 900 400 Q 960 350 1020 400" class="accent-curve" opacity="0.6"/>
</svg>
