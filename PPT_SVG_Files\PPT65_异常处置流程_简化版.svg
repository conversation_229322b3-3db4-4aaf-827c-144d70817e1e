<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #374151; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">异常处置流程（简化版）</text>
  
  <!-- 流程图 -->
  <g transform="translate(960, 500)">
    <!-- 流程主线 -->
    <line x1="-700" y1="0" x2="700" y2="0" stroke="#6B7280" stroke-width="4"/>
    
    <!-- 步骤1：接收与安抚 -->
    <g transform="translate(-560, 0)">
      <circle cx="0" cy="0" r="40" fill="#DC2626"/>
      <text x="0" y="-5" class="content-text" text-anchor="middle" fill="white" font-size="20">1</text>
      <text x="0" y="15" class="content-text" text-anchor="middle" fill="white" font-size="16">接收</text>
      
      <rect x="-80" y="80" width="160" height="120" fill="#FEF2F2" stroke="#DC2626" stroke-width="2" rx="10"/>
      <text x="0" y="110" class="step-title" text-anchor="middle" fill="#DC2626">接收与安抚</text>
      <text x="0" y="140" class="step-text" text-anchor="middle">接到报障/投诉</text>
      <text x="0" y="165" class="step-text" text-anchor="middle">先安抚客户情绪</text>
      <text x="0" y="190" class="step-text" text-anchor="middle">表达理解和歉意</text>
    </g>
    
    <!-- 步骤2：远程诊断 -->
    <g transform="translate(-280, 0)">
      <circle cx="0" cy="0" r="40" fill="#D97706"/>
      <text x="0" y="-5" class="content-text" text-anchor="middle" fill="white" font-size="20">2</text>
      <text x="0" y="15" class="content-text" text-anchor="middle" fill="white" font-size="16">诊断</text>
      
      <rect x="-80" y="80" width="160" height="120" fill="#FEF3C7" stroke="#D97706" stroke-width="2" rx="10"/>
      <text x="0" y="110" class="step-title" text-anchor="middle" fill="#D97706">远程诊断</text>
      <text x="0" y="140" class="step-text" text-anchor="middle">通过平台工具</text>
      <text x="0" y="165" class="step-text" text-anchor="middle">尝试远程定位</text>
      <text x="0" y="190" class="step-text" text-anchor="middle">快速解决问题</text>
    </g>
    
    <!-- 步骤3：精准派单 -->
    <g transform="translate(0, 0)">
      <circle cx="0" cy="0" r="40" fill="#2563EB"/>
      <text x="0" y="-5" class="content-text" text-anchor="middle" fill="white" font-size="20">3</text>
      <text x="0" y="15" class="content-text" text-anchor="middle" fill="white" font-size="16">派单</text>
      
      <rect x="-80" y="80" width="160" height="120" fill="#EFF6FF" stroke="#2563EB" stroke-width="2" rx="10"/>
      <text x="0" y="110" class="step-title" text-anchor="middle" fill="#2563EB">精准派单</text>
      <text x="0" y="140" class="step-text" text-anchor="middle">远程无法解决</text>
      <text x="0" y="165" class="step-text" text-anchor="middle">生成工单</text>
      <text x="0" y="190" class="step-text" text-anchor="middle">信息准确完整</text>
    </g>
    
    <!-- 步骤4：上门处理 -->
    <g transform="translate(280, 0)">
      <circle cx="0" cy="0" r="40" fill="#059669"/>
      <text x="0" y="-5" class="content-text" text-anchor="middle" fill="white" font-size="20">4</text>
      <text x="0" y="15" class="content-text" text-anchor="middle" fill="white" font-size="16">上门</text>
      
      <rect x="-80" y="80" width="160" height="120" fill="#F0FDF4" stroke="#059669" stroke-width="2" rx="10"/>
      <text x="0" y="110" class="step-title" text-anchor="middle" fill="#059669">上门处理</text>
      <text x="0" y="140" class="step-text" text-anchor="middle">标准化操作</text>
      <text x="0" y="165" class="step-text" text-anchor="middle">快速定位</text>
      <text x="0" y="190" class="step-text" text-anchor="middle">彻底修复</text>
    </g>
    
    <!-- 步骤5：闭环反馈 -->
    <g transform="translate(560, 0)">
      <circle cx="0" cy="0" r="40" fill="#8B5CF6"/>
      <text x="0" y="-5" class="content-text" text-anchor="middle" fill="white" font-size="20">5</text>
      <text x="0" y="15" class="content-text" text-anchor="middle" fill="white" font-size="16">闭环</text>
      
      <rect x="-80" y="80" width="160" height="120" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="2" rx="10"/>
      <text x="0" y="110" class="step-title" text-anchor="middle" fill="#8B5CF6">闭环反馈</text>
      <text x="0" y="140" class="step-text" text-anchor="middle">修复后确认</text>
      <text x="0" y="165" class="step-text" text-anchor="middle">客户满意度</text>
      <text x="0" y="190" class="step-text" text-anchor="middle">系统闭环工单</text>
    </g>
    
    <!-- 连接箭头 -->
    <path d="M -480 0 L -360 0" stroke="#6B7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    <path d="M -200 0 L -80 0" stroke="#6B7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    <path d="M 80 0 L 200 0" stroke="#6B7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    <path d="M 360 0 L 480 0" stroke="#6B7280" stroke-width="3" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- 底部说明 -->
  <rect x="200" y="850" width="1520" height="100" fill="#E0F2FE" stroke="#0284C7" stroke-width="2" rx="15"/>
  <text x="960" y="880" class="content-text" text-anchor="middle">关键：每个环节都要快速、准确、专业</text>
  <text x="960" y="910" class="content-text" text-anchor="middle">确保客户问题得到彻底解决</text>
  <text x="960" y="940" class="content-text" text-anchor="middle">提升客户满意度和忠诚度</text>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6B7280"/>
    </marker>
  </defs>
</svg>
