<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .method-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #374151; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #DC2626; font-weight: bold; }
      .sub-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #6B7280; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">WiFi优化常用方法（一线适用）</text>
  
  <!-- 方法1：优化路由器位置 -->
  <rect x="80" y="200" width="1760" height="120" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="15"/>
  <circle cx="150" cy="240" r="15" fill="#059669"/>
  <text x="150" y="248" class="content-text" text-anchor="middle" fill="white" font-size="18">1</text>
  <text x="190" y="230" class="method-title" fill="#059669">优化路由器位置：</text>
  <text x="190" y="260" class="content-text">中心位置、高处、开放空间、远离干扰源</text>
  <text x="190" y="290" class="sub-text">避免放在柜子里、角落或金属物品旁</text>
  
  <!-- 方法2：修改WiFi信道 -->
  <rect x="80" y="340" width="1760" height="120" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="15"/>
  <circle cx="150" cy="380" r="15" fill="#2563EB"/>
  <text x="150" y="388" class="content-text" text-anchor="middle" fill="white" font-size="18">2</text>
  <text x="190" y="370" class="method-title" fill="#2563EB">修改WiFi信道：</text>
  <text x="190" y="400" class="content-text">使用APP或后台，选择干扰少的信道</text>
  <text x="190" y="430" class="sub-text">推荐2.4G使用1、6、11信道，5G自动选择</text>
  
  <!-- 方法3：调整天线角度 -->
  <rect x="80" y="480" width="1760" height="120" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="15"/>
  <circle cx="150" cy="520" r="15" fill="#D97706"/>
  <text x="150" y="528" class="content-text" text-anchor="middle" fill="white" font-size="18">3</text>
  <text x="190" y="510" class="method-title" fill="#D97706">调整天线角度（外置天线）：</text>
  <text x="190" y="540" class="content-text">尝试不同角度组合，垂直和水平搭配</text>
  <text x="190" y="570" class="sub-text">一般建议一根垂直，一根45度角</text>
  
  <!-- 方法4：升级/增添设备 -->
  <rect x="80" y="620" width="1760" height="160" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="15"/>
  <circle cx="150" cy="670" r="15" fill="#DC2626"/>
  <text x="150" y="678" class="content-text" text-anchor="middle" fill="white" font-size="18">4</text>
  <text x="190" y="650" class="method-title" fill="#DC2626">升级/增添设备：</text>
  <text x="220" y="680" class="content-text">• 更换高性能WiFi 6/7路由器</text>
  <text x="220" y="710" class="highlight-text">• 组成Mesh网络：解决大户型/复杂户型覆盖难题（重点推荐）</text>
  <text x="220" y="740" class="sub-text">Mesh是目前最有效的全屋覆盖解决方案</text>
  
  <!-- 方法5：检查网线/接口 -->
  <rect x="80" y="800" width="1760" height="120" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="15"/>
  <circle cx="150" cy="840" r="15" fill="#8B5CF6"/>
  <text x="150" y="848" class="content-text" text-anchor="middle" fill="white" font-size="18">5</text>
  <text x="190" y="830" class="method-title" fill="#8B5CF6">检查网线/接口：</text>
  <text x="190" y="860" class="content-text">确保网线质量（超五类以上）和接口连接牢固</text>
  <text x="190" y="890" class="sub-text">劣质网线会严重影响网速和稳定性</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1650, 500)">
    <!-- WiFi优化图标 -->
    <circle cx="0" cy="0" r="60" fill="none" stroke="#059669" stroke-width="3"/>
    <circle cx="0" cy="0" r="40" fill="none" stroke="#059669" stroke-width="2"/>
    <circle cx="0" cy="0" r="20" fill="#059669"/>
    
    <!-- 优化箭头 -->
    <path d="M -80 -20 L -20 -20" stroke="#DC2626" stroke-width="3" marker-end="url(#arrowhead)"/>
    <path d="M 20 20 L 80 20" stroke="#DC2626" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <text x="0" y="100" class="content-text" text-anchor="middle" font-size="20">WiFi优化</text>
  </g>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#DC2626"/>
    </marker>
  </defs>
</svg>
