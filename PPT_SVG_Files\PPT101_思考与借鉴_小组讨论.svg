<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #8B5CF6; text-anchor: middle; }
      .question-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 38px; fill: #DC2626; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="220" class="title-text">思考与借鉴</text>
  <text x="960" y="290" class="subtitle-text">（小组讨论）</text>
  
  <!-- 讨论问题1 -->
  <rect x="150" y="340" width="1620" height="200" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="30"/>
  <circle cx="220" cy="410" r="20" fill="#DC2626"/>
  <text x="220" y="420" class="content-text" text-anchor="middle" fill="white" font-size="24">1</text>
  <text x="270" y="390" class="question-text">浙江模式哪些最值得我们学？</text>
  <text x="270" y="440" class="content-text">分析浙江移动成功经验中的核心要素</text>
  <text x="270" y="480" class="content-text">识别可复制、可推广的关键做法</text>
  <text x="270" y="520" class="content-text">思考在四川的适用性和可行性</text>
  
  <!-- 讨论问题2 -->
  <rect x="150" y="560" width="1620" height="240" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="30"/>
  <circle cx="220" cy="650" r="20" fill="#2563EB"/>
  <text x="220" y="660" class="content-text" text-anchor="middle" fill="white" font-size="24">2</text>
  <text x="270" y="620" class="question-text" fill="#2563EB">结合四川实际，哪些可以马上用？</text>
  <text x="270" y="620" class="question-text" fill="#2563EB">哪些需要调整？</text>
  <text x="270" y="680" class="content-text">立即可行：现有资源和条件下能直接应用的</text>
  <text x="270" y="720" class="content-text">需要调整：结合四川市场特点需要改进的</text>
  <text x="270" y="760" class="content-text">长期规划：需要逐步建设和完善的</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1500, 550)">
    <!-- 思考讨论图标 -->
    <circle cx="0" cy="0" r="100" fill="none" stroke="#8B5CF6" stroke-width="4"/>
    <circle cx="0" cy="0" r="70" fill="#8B5CF6" opacity="0.1"/>
    
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="28" fill="#8B5CF6">思考</text>
    <text x="0" y="25" class="content-text" text-anchor="middle" font-size="28" fill="#8B5CF6">借鉴</text>
    
    <!-- 讨论元素 -->
    <circle cx="-60" cy="-60" r="20" fill="#DC2626" opacity="0.6"/>
    <text x="-60" y="-50" class="content-text" text-anchor="middle" fill="white" font-size="16">学</text>
    
    <circle cx="60" cy="-60" r="20" fill="#2563EB" opacity="0.6"/>
    <text x="60" y="-50" class="content-text" text-anchor="middle" fill="white" font-size="16">用</text>
    
    <circle cx="-60" cy="60" r="20" fill="#059669" opacity="0.6"/>
    <text x="-60" y="70" class="content-text" text-anchor="middle" fill="white" font-size="16">改</text>
    
    <circle cx="60" cy="60" r="20" fill="#D97706" opacity="0.6"/>
    <text x="60" y="70" class="content-text" text-anchor="middle" fill="white" font-size="16">创</text>
    
    <text x="0" y="140" class="content-text" text-anchor="middle" font-size="20">小组讨论</text>
  </g>
</svg>
