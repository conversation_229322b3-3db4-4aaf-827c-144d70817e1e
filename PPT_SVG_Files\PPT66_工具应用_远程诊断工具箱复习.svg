<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .tool-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: bold; }
      .value-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">工具应用：远程诊断工具箱（复习）</text>
  
  <!-- 工具1：爱家APP一键诊断 -->
  <rect x="100" y="220" width="1720" height="140" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="270" r="20" fill="#2563EB"/>
  <text x="180" y="280" class="content-text" text-anchor="middle" fill="white" font-size="18">APP</text>
  <text x="230" y="260" class="section-title" fill="#2563EB">爱家APP一键诊断：</text>
  <text x="230" y="300" class="tool-text" fill="#2563EB">引导客户使用，自助检测网络状态</text>
  <text x="230" y="340" class="content-text">简单易用，客户可自主操作</text>
  
  <!-- 工具2：平台远程拨测 -->
  <rect x="100" y="380" width="1720" height="140" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="430" r="20" fill="#059669"/>
  <text x="180" y="440" class="content-text" text-anchor="middle" fill="white" font-size="16">拨测</text>
  <text x="230" y="420" class="section-title" fill="#059669">平台远程拨测：</text>
  <text x="230" y="460" class="tool-text" fill="#059669">后台主动检测，实时监控网络质量</text>
  <text x="230" y="500" class="content-text">无需客户配合，快速定位问题</text>
  
  <!-- 工具3：WiFi质量分析 -->
  <rect x="100" y="540" width="1720" height="140" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="590" r="20" fill="#D97706"/>
  <text x="180" y="600" class="content-text" text-anchor="middle" fill="white" font-size="16">WiFi</text>
  <text x="230" y="580" class="section-title" fill="#D97706">WiFi质量分析：</text>
  <text x="230" y="620" class="tool-text" fill="#D97706">热力图分析，精准定位覆盖问题</text>
  <text x="230" y="660" class="content-text">可视化展示，便于客户理解</text>
  
  <!-- 价值体现 -->
  <rect x="100" y="700" width="1720" height="200" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="780" r="20" fill="#DC2626"/>
  <text x="180" y="790" class="content-text" text-anchor="middle" fill="white" font-size="18">价值</text>
  <text x="230" y="760" class="section-title" fill="#DC2626">核心价值：</text>
  <text x="230" y="810" class="value-text">提升首次解决率</text>
  <text x="230" y="850" class="value-text">减少无效上门</text>
  <text x="230" y="890" class="content-text">节约成本，提升效率，改善客户体验</text>
  
  <!-- 装饰元素 - 工具箱示意图 -->
  <g transform="translate(1500, 500)">
    <!-- 工具箱 -->
    <rect x="0" y="0" width="300" height="200" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="3" rx="15"/>
    <text x="150" y="30" class="content-text" text-anchor="middle" font-size="24">远程诊断工具箱</text>
    
    <!-- 工具图标 -->
    <g transform="translate(50, 60)">
      <!-- APP图标 -->
      <rect x="0" y="0" width="60" height="40" fill="#2563EB" opacity="0.6" rx="5"/>
      <text x="30" y="25" class="content-text" text-anchor="middle" fill="white" font-size="16">APP</text>
      
      <!-- 拨测图标 -->
      <rect x="80" y="0" width="60" height="40" fill="#059669" opacity="0.6" rx="5"/>
      <text x="110" y="25" class="content-text" text-anchor="middle" fill="white" font-size="16">拨测</text>
      
      <!-- WiFi图标 -->
      <rect x="160" y="0" width="60" height="40" fill="#D97706" opacity="0.6" rx="5"/>
      <text x="190" y="25" class="content-text" text-anchor="middle" fill="white" font-size="16">WiFi</text>
      
      <!-- 其他工具 -->
      <rect x="0" y="60" width="60" height="40" fill="#8B5CF6" opacity="0.6" rx="5"/>
      <text x="30" y="85" class="content-text" text-anchor="middle" fill="white" font-size="16">监控</text>
      
      <rect x="80" y="60" width="60" height="40" fill="#DC2626" opacity="0.6" rx="5"/>
      <text x="110" y="85" class="content-text" text-anchor="middle" fill="white" font-size="16">告警</text>
      
      <rect x="160" y="60" width="60" height="40" fill="#6B7280" opacity="0.6" rx="5"/>
      <text x="190" y="85" class="content-text" text-anchor="middle" fill="white" font-size="16">分析</text>
    </g>
    
    <text x="150" y="180" class="content-text" text-anchor="middle" font-size="20">一站式诊断</text>
  </g>
</svg>
