<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .scenario-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #D97706; font-weight: bold; }
      .task-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #059669; }
      .tip-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #8B5CF6; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">挽留话术演练：场景2（竞品优惠诱惑）</text>
  
  <!-- 场景描述 -->
  <rect x="100" y="220" width="1720" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="20" fill="#D97706"/>
  <text x="180" y="300" class="content-text" text-anchor="middle" fill="white" font-size="20">场景</text>
  <text x="230" y="270" class="section-title" fill="#D97706">演练场景：</text>
  <text x="230" y="320" class="scenario-text">竞争对手推出超低价套餐</text>
  <text x="230" y="360" class="content-text">客户被价格优势吸引，准备转网</text>
  <text x="230" y="400" class="content-text">对移动服务基本满意，但价格敏感</text>
  
  <!-- 演练任务 -->
  <rect x="100" y="440" width="1720" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="510" r="20" fill="#059669"/>
  <text x="180" y="520" class="content-text" text-anchor="middle" fill="white" font-size="20">任务</text>
  <text x="230" y="490" class="section-title" fill="#059669">演练任务：</text>
  <text x="230" y="540" class="task-text">分组演练，模拟竞品挽留沟通</text>
  <text x="230" y="580" class="task-text">重点展示价值对比和差异化优势</text>
  <text x="230" y="620" class="task-text">设计有竞争力的挽留方案</text>
  
  <!-- 重点提示 -->
  <rect x="100" y="660" width="1720" height="280" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="750" r="20" fill="#2563EB"/>
  <text x="180" y="760" class="content-text" text-anchor="middle" fill="white" font-size="20">提示</text>
  <text x="230" y="730" class="section-title" fill="#2563EB">重点提示：</text>
  <g transform="translate(280, 770)">
    <circle cx="0" cy="0" r="8" fill="#2563EB"/>
    <text x="20" y="8" class="tip-text">了解竞品方案，做好对比分析</text>
    
    <circle cx="0" cy="40" r="8" fill="#2563EB"/>
    <text x="20" y="48" class="tip-text">突出移动网络/服务优势</text>
    
    <circle cx="0" cy="80" r="8" fill="#2563EB"/>
    <text x="20" y="88" class="tip-text">提供对标或差异化优惠</text>
    
    <circle cx="0" cy="120" r="8" fill="#2563EB"/>
    <text x="20" y="128" class="tip-text">强调长期价值，不只看价格</text>
    
    <circle cx="0" cy="160" r="8" fill="#2563EB"/>
    <text x="20" y="168" class="content-text">算总账：网络质量+服务+优惠</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <!-- 竞争对比图 -->
    <rect x="-100" y="-80" width="200" height="160" fill="none" stroke="#D97706" stroke-width="3" rx="15"/>
    <text x="0" y="-50" class="content-text" text-anchor="middle" font-size="20" fill="#D97706">竞品对比</text>
    
    <!-- 移动优势 -->
    <rect x="-80" y="-20" width="60" height="30" fill="#059669" opacity="0.6" rx="5"/>
    <text x="-50" y="0" class="content-text" text-anchor="middle" fill="white" font-size="14">网络</text>
    
    <rect x="-10" y="-20" width="60" height="30" fill="#059669" opacity="0.6" rx="5"/>
    <text x="20" y="0" class="content-text" text-anchor="middle" fill="white" font-size="14">服务</text>
    
    <!-- 竞品优势 -->
    <rect x="-80" y="20" width="60" height="30" fill="#DC2626" opacity="0.6" rx="5"/>
    <text x="-50" y="40" class="content-text" text-anchor="middle" fill="white" font-size="14">价格</text>
    
    <!-- 平衡点 -->
    <circle cx="20" cy="35" r="15" fill="#2563EB" opacity="0.6"/>
    <text x="20" y="40" class="content-text" text-anchor="middle" fill="white" font-size="12">平衡</text>
    
    <text x="0" y="120" class="content-text" text-anchor="middle" font-size="20">价值对比</text>
  </g>
</svg>
