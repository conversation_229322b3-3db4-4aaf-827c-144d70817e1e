<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #374151; }
      .template-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #6B7280; }
      .field-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #2563EB; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">工具：90天改进计划书模板</text>
  
  <!-- 模板结构 -->
  <rect x="100" y="200" width="1720" height="720" fill="#F9FAFB" stroke="#6B7280" stroke-width="3" rx="20"/>
  <circle cx="180" cy="450" r="20" fill="#6B7280"/>
  <text x="180" y="460" class="content-text" text-anchor="middle" fill="white" font-size="18">模板</text>
  <text x="230" y="250" class="section-title" fill="#6B7280">截图/表格：展示模板结构</text>
  
  <!-- 模板字段 -->
  <g transform="translate(280, 290)">
    <!-- 基本信息 -->
    <rect x="0" y="0" width="1300" height="50" fill="#EFF6FF" stroke="#2563EB" stroke-width="1" rx="5"/>
    <text x="20" y="25" class="field-text">区域/团队：</text>
    <text x="150" y="25" class="template-text">_________________</text>
    <text x="400" y="25" class="template-text">（填写具体区域或团队名称）</text>
    
    <!-- 问题诊断 -->
    <rect x="0" y="70" width="1300" height="80" fill="#FEF2F2" stroke="#DC2626" stroke-width="1" rx="5"/>
    <text x="20" y="95" class="field-text">核心问题诊断：</text>
    <text x="20" y="120" class="template-text">（1-2个最突出的）</text>
    <text x="20" y="140" class="template-text">问题1：_________________________________</text>
    
    <!-- 改进目标 -->
    <rect x="0" y="170" width="1300" height="60" fill="#F0FDF4" stroke="#059669" stroke-width="1" rx="5"/>
    <text x="20" y="195" class="field-text">改进总目标（90天）：</text>
    <text x="250" y="195" class="template-text">（SMART原则）</text>
    <text x="20" y="220" class="template-text">目标：_________________________________</text>
    
    <!-- 关键动作1 -->
    <rect x="0" y="250" width="1300" height="120" fill="#FEF3C7" stroke="#D97706" stroke-width="1" rx="5"/>
    <text x="20" y="275" class="field-text">关键动作1：</text>
    <text x="20" y="300" class="template-text">具体做什么：_________________________</text>
    <text x="20" y="325" class="template-text">衡量标准：___________________________</text>
    <text x="20" y="350" class="template-text">责任人：_____________________________</text>
    
    <!-- 关键动作2 -->
    <rect x="0" y="390" width="1300" height="80" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="1" rx="5"/>
    <text x="20" y="415" class="field-text">关键动作2：</text>
    <text x="20" y="440" class="template-text">时间节点：___________________________</text>
    <text x="20" y="460" class="template-text">所需资源/支持：_____________________</text>
    
    <!-- 关键动作3 -->
    <rect x="0" y="490" width="1300" height="80" fill="#DBEAFE" stroke="#3B82F6" stroke-width="1" rx="5"/>
    <text x="20" y="515" class="field-text">关键动作3：</text>
    <text x="20" y="540" class="template-text">（可根据实际情况增加或减少动作项）</text>
    <text x="20" y="560" class="template-text">具体内容：___________________________</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 600)">
    <rect x="0" y="0" width="200" height="250" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="100" y="30" class="content-text" text-anchor="middle" font-size="20">SMART原则</text>
    
    <g transform="translate(20, 50)">
      <text x="0" y="20" class="content-text" font-size="16" fill="#2563EB">S - 具体明确</text>
      <text x="0" y="45" class="content-text" font-size="16" fill="#059669">M - 可以衡量</text>
      <text x="0" y="70" class="content-text" font-size="16" fill="#D97706">A - 可以达成</text>
      <text x="0" y="95" class="content-text" font-size="16" fill="#DC2626">R - 相关重要</text>
      <text x="0" y="120" class="content-text" font-size="16" fill="#8B5CF6">T - 时限明确</text>
    </g>
    
    <text x="100" y="220" class="content-text" text-anchor="middle" font-size="18">目标制定</text>
  </g>
</svg>
