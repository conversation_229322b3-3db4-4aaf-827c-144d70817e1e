<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .key-point { font-family: 'Microsoft YaHei', sans-serif; font-size: 38px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  <path d="M 100 960 Q 960 1060 1820 960" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">模块四总结：关键要点回顾</text>
  
  <!-- 要点1 -->
  <rect x="100" y="240" width="1720" height="140" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="20" fill="#8B5CF6"/>
  <text x="180" y="300" class="content-text" text-anchor="middle" fill="white" font-size="24">1</text>
  <text x="230" y="300" class="key-point" fill="#8B5CF6">关注CLV，挖掘客户长期价值</text>
  <text x="230" y="340" class="content-text">从用户思维转向资产思维，全生命周期运营</text>
  
  <!-- 要点2 -->
  <rect x="100" y="400" width="1720" height="140" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="450" r="20" fill="#2563EB"/>
  <text x="180" y="460" class="content-text" text-anchor="middle" fill="white" font-size="24">2</text>
  <text x="230" y="460" class="key-point" fill="#2563EB">智能推荐提升营销效率和客户体验</text>
  <text x="230" y="500" class="content-text">精准推荐，千人千面，提升转化率</text>
  
  <!-- 要点3 -->
  <rect x="100" y="560" width="1720" height="140" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="610" r="20" fill="#059669"/>
  <text x="180" y="620" class="content-text" text-anchor="middle" fill="white" font-size="24">3</text>
  <text x="230" y="620" class="key-point" fill="#059669">场景化营销更能打动客户</text>
  <text x="230" y="660" class="content-text">让产品融入生活，用场景说话</text>
  
  <!-- 要点4 -->
  <rect x="100" y="720" width="1720" height="140" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="770" r="20" fill="#D97706"/>
  <text x="180" y="780" class="content-text" text-anchor="middle" fill="white" font-size="24">4</text>
  <text x="230" y="780" class="key-point" fill="#D97706">网格化运营实现区域精耕细作</text>
  <text x="230" y="820" class="content-text">精准协同闭环，提升运营效率</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <circle cx="0" cy="0" r="80" fill="none" stroke="#3B82F6" stroke-width="4" opacity="0.3"/>
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="28">模块四</text>
    <text x="0" y="20" class="content-text" text-anchor="middle" font-size="28">完成</text>
    <circle cx="0" cy="0" r="50" fill="#8B5CF6" opacity="0.2"/>
    
    <!-- 价值深耕图标 -->
    <g transform="translate(0, 120)">
      <circle cx="0" cy="0" r="40" fill="none" stroke="#8B5CF6" stroke-width="3"/>
      <path d="M -15 0 L -5 10 L 15 -10" stroke="#8B5CF6" stroke-width="4" fill="none"/>
      <text x="0" y="60" class="content-text" text-anchor="middle" font-size="18" fill="#8B5CF6">价值深耕</text>
    </g>
  </g>
</svg>
