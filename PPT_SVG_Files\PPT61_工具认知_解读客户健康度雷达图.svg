<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .dimension-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2563EB; }
      .rule-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">工具认知：解读客户健康度雷达图</text>
  
  <!-- 这是什么 -->
  <rect x="100" y="200" width="1720" height="120" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="240" r="15" fill="#2563EB"/>
  <text x="220" y="230" class="section-title" fill="#2563EB">这是什么：</text>
  <text x="220" y="270" class="content-text">用户状态的可视化仪表盘</text>
  <text x="220" y="300" class="content-text">直观展示客户各维度健康状况</text>
  
  <!-- 维度 -->
  <rect x="100" y="340" width="1720" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="410" r="15" fill="#059669"/>
  <text x="220" y="390" class="section-title" fill="#059669">关键维度：</text>
  <g transform="translate(270, 420)">
    <circle cx="0" cy="0" r="8" fill="#059669"/>
    <text x="20" y="8" class="dimension-text">网络质量、服务交互、价值贡献</text>
    
    <circle cx="0" cy="40" r="8" fill="#059669"/>
    <text x="20" y="48" class="dimension-text">合约状态、行为活跃度等</text>
    
    <circle cx="0" cy="80" r="8" fill="#059669"/>
    <text x="20" y="88" class="content-text">多维度综合评估客户健康状态</text>
  </g>
  
  <!-- 如何看 -->
  <rect x="100" y="560" width="1720" height="240" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="640" r="15" fill="#D97706"/>
  <text x="220" y="620" class="section-title" fill="#D97706">如何解读：</text>
  <g transform="translate(270, 650)">
    <circle cx="0" cy="0" r="8" fill="#059669"/>
    <text x="20" y="8" class="rule-text" fill="#059669">图形饱满、均衡 → 健康</text>
    
    <circle cx="0" cy="40" r="8" fill="#D97706"/>
    <text x="20" y="48" class="rule-text" fill="#D97706">某个维度凹陷 → 存在风险</text>
    
    <circle cx="0" cy="80" r="8" fill="#DC2626"/>
    <text x="20" y="88" class="rule-text" fill="#DC2626">整体图形萎缩 → 风险高</text>
    
    <circle cx="0" cy="120" r="8" fill="#6B7280"/>
    <text x="20" y="128" class="content-text">需要重点关注和干预</text>
  </g>
  
  <!-- 雷达图示例 -->
  <g transform="translate(1200, 600)">
    <!-- 健康型 -->
    <g transform="translate(-300, -150)">
      <circle cx="0" cy="0" r="80" fill="none" stroke="#E5E7EB" stroke-width="1"/>
      <circle cx="0" cy="0" r="40" fill="none" stroke="#E5E7EB" stroke-width="1"/>
      <line x1="0" y1="-80" x2="0" y2="80" stroke="#E5E7EB" stroke-width="1"/>
      <line x1="-80" y1="0" x2="80" y2="0" stroke="#E5E7EB" stroke-width="1"/>
      <polygon points="0,-70 50,-50 60,10 40,60 -40,50 -60,-40" 
               fill="#059669" opacity="0.4" stroke="#059669" stroke-width="2"/>
      <text x="0" y="110" class="content-text" text-anchor="middle" font-size="20" fill="#059669">健康型</text>
    </g>
    
    <!-- 质差型 -->
    <g transform="translate(0, -150)">
      <circle cx="0" cy="0" r="80" fill="none" stroke="#E5E7EB" stroke-width="1"/>
      <circle cx="0" cy="0" r="40" fill="none" stroke="#E5E7EB" stroke-width="1"/>
      <line x1="0" y1="-80" x2="0" y2="80" stroke="#E5E7EB" stroke-width="1"/>
      <line x1="-80" y1="0" x2="80" y2="0" stroke="#E5E7EB" stroke-width="1"/>
      <polygon points="0,-20 50,-50 60,10 40,60 -40,50 -60,-40" 
               fill="#DC2626" opacity="0.4" stroke="#DC2626" stroke-width="2"/>
      <text x="0" y="110" class="content-text" text-anchor="middle" font-size="20" fill="#DC2626">质差型</text>
    </g>
    
    <!-- 低价值型 -->
    <g transform="translate(-300, 150)">
      <circle cx="0" cy="0" r="80" fill="none" stroke="#E5E7EB" stroke-width="1"/>
      <circle cx="0" cy="0" r="40" fill="none" stroke="#E5E7EB" stroke-width="1"/>
      <line x1="0" y1="-80" x2="0" y2="80" stroke="#E5E7EB" stroke-width="1"/>
      <line x1="-80" y1="0" x2="80" y2="0" stroke="#E5E7EB" stroke-width="1"/>
      <polygon points="0,-60 20,-30 30,5 20,30 -20,25 -30,-20" 
               fill="#D97706" opacity="0.4" stroke="#D97706" stroke-width="2"/>
      <text x="0" y="110" class="content-text" text-anchor="middle" font-size="20" fill="#D97706">低价值型</text>
    </g>
    
    <!-- 到期型 -->
    <g transform="translate(0, 150)">
      <circle cx="0" cy="0" r="80" fill="none" stroke="#E5E7EB" stroke-width="1"/>
      <circle cx="0" cy="0" r="40" fill="none" stroke="#E5E7EB" stroke-width="1"/>
      <line x1="0" y1="-80" x2="0" y2="80" stroke="#E5E7EB" stroke-width="1"/>
      <line x1="-80" y1="0" x2="80" y2="0" stroke="#E5E7EB" stroke-width="1"/>
      <polygon points="0,-60 50,-50 20,10 40,60 -40,50 -60,-40" 
               fill="#8B5CF6" opacity="0.4" stroke="#8B5CF6" stroke-width="2"/>
      <text x="0" y="110" class="content-text" text-anchor="middle" font-size="20" fill="#8B5CF6">到期型</text>
    </g>
  </g>
</svg>
