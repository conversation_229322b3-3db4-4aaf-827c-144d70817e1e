<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .scenario-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #DC2626; font-weight: bold; }
      .task-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #059669; }
      .tip-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #8B5CF6; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">挽留话术演练：场景1（高频断网拆机）</text>
  
  <!-- 场景描述 -->
  <rect x="100" y="220" width="1720" height="200" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="20" fill="#DC2626"/>
  <text x="180" y="300" class="content-text" text-anchor="middle" fill="white" font-size="20">场景</text>
  <text x="230" y="270" class="section-title" fill="#DC2626">演练场景：</text>
  <text x="230" y="320" class="scenario-text">客户因网络频繁断线要求拆机</text>
  <text x="230" y="360" class="content-text">已经影响工作和生活，情绪激动</text>
  <text x="230" y="400" class="content-text">多次报修未彻底解决，失去信心</text>
  
  <!-- 演练任务 -->
  <rect x="100" y="440" width="1720" height="240" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="530" r="20" fill="#059669"/>
  <text x="180" y="540" class="content-text" text-anchor="middle" fill="white" font-size="20">任务</text>
  <text x="230" y="510" class="section-title" fill="#059669">演练任务：</text>
  <text x="230" y="560" class="task-text">分组演练，运用八步法进行模拟沟通</text>
  <text x="230" y="600" class="task-text">每组选派代表扮演客户和客服</text>
  <text x="230" y="640" class="task-text">重点展示情绪安抚和解决方案</text>
  
  <!-- 重点提示 -->
  <rect x="100" y="700" width="1720" height="240" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="790" r="20" fill="#2563EB"/>
  <text x="180" y="800" class="content-text" text-anchor="middle" fill="white" font-size="20">提示</text>
  <text x="230" y="770" class="section-title" fill="#2563EB">重点提示：</text>
  <g transform="translate(280, 810)">
    <circle cx="0" cy="0" r="8" fill="#2563EB"/>
    <text x="20" y="8" class="tip-text">情绪安抚是关键，先稳定再解决</text>
    
    <circle cx="0" cy="40" r="8" fill="#2563EB"/>
    <text x="20" y="48" class="tip-text">提供有吸引力的解决方案</text>
    <text x="50" y="78" class="content-text">（如免费换设备+话费补偿）</text>
    
    <circle cx="0" cy="110" r="8" fill="#2563EB"/>
    <text x="20" y="118" class="tip-text">承诺具体时间和跟进措施</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <!-- 演练示意图 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#DC2626" stroke-width="3" opacity="0.3"/>
    
    <!-- 客户情绪 -->
    <circle cx="-40" cy="-40" r="25" fill="#DC2626" opacity="0.6"/>
    <text x="-40" y="-30" class="content-text" text-anchor="middle" fill="white" font-size="16">客户</text>
    <text x="-40" y="-15" class="content-text" text-anchor="middle" fill="white" font-size="14">愤怒</text>
    
    <!-- 客服应对 -->
    <circle cx="40" cy="40" r="25" fill="#059669" opacity="0.6"/>
    <text x="40" y="50" class="content-text" text-anchor="middle" fill="white" font-size="16">客服</text>
    <text x="40" y="65" class="content-text" text-anchor="middle" fill="white" font-size="14">安抚</text>
    
    <!-- 连接线 -->
    <path d="M -20 -20 L 20 20" stroke="#2563EB" stroke-width="2" stroke-dasharray="5,5"/>
    
    <text x="0" y="120" class="content-text" text-anchor="middle" font-size="20">情绪转化</text>
  </g>
</svg>
