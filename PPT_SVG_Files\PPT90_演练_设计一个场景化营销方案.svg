<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .task-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #059669; font-weight: bold; }
      .target-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #8B5CF6; }
      .requirement-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #DC2626; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">演练：设计一个场景化营销方案</text>
  
  <!-- 任务描述 -->
  <rect x="100" y="220" width="1720" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="20" fill="#059669"/>
  <text x="180" y="300" class="content-text" text-anchor="middle" fill="white" font-size="20">任务</text>
  <text x="230" y="270" class="section-title" fill="#059669">演练任务：</text>
  <text x="230" y="320" class="task-text">针对特定用户群体，设计场景化营销方案</text>
  <text x="230" y="360" class="task-text">包含宽带、智能硬件、应用服务</text>
  <text x="230" y="400" class="task-text">配套沟通话术和推广策略</text>
  
  <!-- 目标群体选择 -->
  <rect x="100" y="440" width="1720" height="240" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="530" r="20" fill="#8B5CF6"/>
  <text x="180" y="540" class="content-text" text-anchor="middle" fill="white" font-size="18">群体</text>
  <text x="230" y="510" class="section-title" fill="#8B5CF6">目标群体选择：</text>
  
  <g transform="translate(280, 550)">
    <rect x="0" y="0" width="600" height="60" fill="#E0E7FF" rx="10"/>
    <text x="20" y="25" class="target-text">选项A：有宠物的年轻租房群体</text>
    <text x="20" y="50" class="content-text" font-size="22">关注便利性、智能化、成本控制</text>
    
    <rect x="650" y="0" width="600" height="60" fill="#F3E8FF" rx="10"/>
    <text x="670" y="25" class="target-text">选项B：注重健康的退休老人群体</text>
    <text x="670" y="50" class="content-text" font-size="22">关注健康监测、安全防护、简单易用</text>
  </g>
  
  <!-- 设计要求 -->
  <rect x="100" y="700" width="1720" height="240" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="790" r="20" fill="#DC2626"/>
  <text x="180" y="800" class="content-text" text-anchor="middle" fill="white" font-size="18">要求</text>
  <text x="230" y="770" class="section-title" fill="#DC2626">设计要求：</text>
  
  <g transform="translate(280, 810)">
    <circle cx="0" cy="0" r="8" fill="#DC2626"/>
    <text x="20" y="8" class="requirement-text">深入分析目标群体的生活场景和痛点</text>
    
    <circle cx="0" cy="40" r="8" fill="#DC2626"/>
    <text x="20" y="48" class="requirement-text">设计包含宽带+硬件+服务的综合方案</text>
    
    <circle cx="0" cy="80" r="8" fill="#DC2626"/>
    <text x="20" y="88" class="requirement-text">编写生动的场景化沟通话术</text>
    
    <circle cx="0" cy="120" r="8" fill="#DC2626"/>
    <text x="20" y="128" class="requirement-text">制定具体的推广策略和价格方案</text>
  </g>
  
  <!-- 装饰元素 - 设计思路图 -->
  <g transform="translate(1500, 600)">
    <!-- 设计流程 -->
    <rect x="0" y="0" width="300" height="300" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="150" y="30" class="content-text" text-anchor="middle" font-size="24">设计思路</text>
    
    <!-- 设计步骤 -->
    <g transform="translate(50, 60)">
      <circle cx="0" cy="0" r="20" fill="#8B5CF6" opacity="0.6"/>
      <text x="0" y="5" class="content-text" text-anchor="middle" fill="white" font-size="16">1</text>
      <text x="30" y="8" class="content-text" font-size="18">分析群体</text>
      
      <circle cx="0" cy="60" r="20" fill="#059669" opacity="0.6"/>
      <text x="0" y="65" class="content-text" text-anchor="middle" fill="white" font-size="16">2</text>
      <text x="30" y="68" class="content-text" font-size="18">场景设计</text>
      
      <circle cx="0" cy="120" r="20" fill="#D97706" opacity="0.6"/>
      <text x="0" y="125" class="content-text" text-anchor="middle" fill="white" font-size="16">3</text>
      <text x="30" y="128" class="content-text" font-size="18">方案组合</text>
      
      <circle cx="0" cy="180" r="20" fill="#DC2626" opacity="0.6"/>
      <text x="0" y="185" class="content-text" text-anchor="middle" fill="white" font-size="16">4</text>
      <text x="30" y="188" class="content-text" font-size="18">话术编写</text>
      
      <!-- 连接线 -->
      <line x1="0" y1="20" x2="0" y2="40" stroke="#6B7280" stroke-width="2"/>
      <line x1="0" y1="80" x2="0" y2="100" stroke="#6B7280" stroke-width="2"/>
      <line x1="0" y1="140" x2="0" y2="160" stroke="#6B7280" stroke-width="2"/>
    </g>
    
    <text x="150" y="280" class="content-text" text-anchor="middle" font-size="20">系统设计</text>
  </g>
</svg>
