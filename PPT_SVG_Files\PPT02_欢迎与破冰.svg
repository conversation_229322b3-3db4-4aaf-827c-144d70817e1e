<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; fill: #374151; text-anchor: middle; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #4B5563; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="200" class="title-text">欢迎与破冰</text>
  
  <!-- 欢迎致辞 -->
  <circle cx="200" cy="350" r="12" fill="#3B82F6"/>
  <text x="250" y="370" class="subtitle-text">欢迎各位四川移动的精英伙伴！</text>
  
  <!-- 破冰活动 -->
  <circle cx="200" cy="480" r="12" fill="#10B981"/>
  <text x="250" y="500" class="content-text">破冰活动：</text>
  <text x="250" y="560" class="highlight-text">"我的家宽印象"关键词分享</text>
  
  <!-- 装饰框 -->
  <rect x="150" y="620" width="1620" height="280" fill="none" stroke="#E5E7EB" stroke-width="2" rx="20"/>
  
  <!-- 目的说明 -->
  <circle cx="200" cy="680" r="12" fill="#F59E0B"/>
  <text x="250" y="700" class="content-text">活动目的：</text>
  
  <text x="300" y="760" class="content-text">• 快速融入学习氛围</text>
  <text x="300" y="820" class="content-text">• 了解大家对家宽业务的认知和期待</text>
  <text x="300" y="880" class="content-text">• 营造积极互动的培训环境</text>
  
  <!-- 装饰元素 -->
  <path d="M 1400 400 Q 1500 350 1600 400" class="accent-curve" opacity="0.6"/>
  <path d="M 1400 700 Q 1500 650 1600 700" class="accent-curve" opacity="0.6"/>
</svg>
