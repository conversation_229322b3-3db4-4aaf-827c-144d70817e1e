<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #DC2626; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .method-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #2563EB; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">六必查 2/6：端口资源核查</text>
  
  <!-- 查什么 -->
  <rect x="100" y="220" width="1720" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="15" fill="#059669"/>
  <text x="220" y="270" class="section-title">查什么：</text>
  <text x="220" y="320" class="content-text">• 系统端口与现场标签是否一致？</text>
  <text x="220" y="370" class="content-text">• 端口是否空闲可用？</text>
  
  <!-- 为何查 -->
  <rect x="100" y="440" width="1720" height="200" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="510" r="15" fill="#2563EB"/>
  <text x="220" y="490" class="section-title">为何查：</text>
  <text x="220" y="540" class="content-text">避免端口错占、资源不清导致装机失败</text>
  <text x="220" y="590" class="content-text">或影响其他客户正常使用</text>
  
  <!-- 怎么查 -->
  <rect x="100" y="660" width="1720" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="730" r="15" fill="#D97706"/>
  <text x="220" y="710" class="section-title">怎么查：</text>
  <text x="220" y="760" class="method-text">• 核对工单信息</text>
  <text x="220" y="800" class="method-text">• 查看系统资源图</text>
  <text x="220" y="840" class="method-text">• 检查现场分光器/面板标签</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1500, 400)">
    <!-- 端口示意图 -->
    <rect x="0" y="0" width="300" height="200" fill="none" stroke="#6B7280" stroke-width="3" rx="10"/>
    <text x="150" y="30" class="content-text" text-anchor="middle" font-size="24">分光器</text>
    
    <!-- 端口 -->
    <circle cx="50" cy="80" r="15" fill="#059669"/>
    <text x="50" y="110" class="content-text" text-anchor="middle" font-size="16">1</text>
    
    <circle cx="150" cy="80" r="15" fill="#DC2626"/>
    <text x="150" y="110" class="content-text" text-anchor="middle" font-size="16">2</text>
    
    <circle cx="250" cy="80" r="15" fill="#6B7280"/>
    <text x="250" y="110" class="content-text" text-anchor="middle" font-size="16">3</text>
    
    <circle cx="50" cy="150" r="15" fill="#6B7280"/>
    <text x="50" y="180" class="content-text" text-anchor="middle" font-size="16">4</text>
    
    <circle cx="150" cy="150" r="15" fill="#6B7280"/>
    <text x="150" y="180" class="content-text" text-anchor="middle" font-size="16">5</text>
    
    <circle cx="250" cy="150" r="15" fill="#6B7280"/>
    <text x="250" y="180" class="content-text" text-anchor="middle" font-size="16">6</text>
  </g>
  
  <!-- 图例 -->
  <g transform="translate(1500, 650)">
    <circle cx="0" cy="0" r="10" fill="#059669"/>
    <text x="25" y="8" class="content-text" font-size="20">空闲</text>
    
    <circle cx="0" cy="40" r="10" fill="#DC2626"/>
    <text x="25" y="48" class="content-text" font-size="20">占用</text>
    
    <circle cx="0" cy="80" r="10" fill="#6B7280"/>
    <text x="25" y="88" class="content-text" font-size="20">未知</text>
  </g>
</svg>
