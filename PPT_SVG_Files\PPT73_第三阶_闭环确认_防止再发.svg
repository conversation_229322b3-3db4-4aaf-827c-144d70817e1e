<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .phase-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #059669; font-weight: bold; text-anchor: middle; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #059669; font-style: italic; }
      .tip-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #8B5CF6; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">第三阶：闭环确认（防止再发）</text>
  
  <!-- Step 7: 执行与跟踪 -->
  <rect x="100" y="220" width="1720" height="280" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="320" r="25" fill="#059669"/>
  <text x="180" y="330" class="content-text" text-anchor="middle" fill="white" font-size="24">7</text>
  <text x="230" y="280" class="step-title" fill="#059669">Step 7: 执行与跟踪</text>
  <text x="230" y="320" class="content-text">明确行动计划，承诺跟进</text>
  
  <rect x="250" y="350" width="1400" height="120" fill="#DCFCE7" rx="10"/>
  <text x="270" y="380" class="example-text">"好的，我已经安排技术人员明天上午9点到您家</text>
  <text x="270" y="410" class="example-text">更换光纤设备，预计1小时完成。</text>
  <text x="270" y="440" class="example-text">我会全程跟进，有任何问题随时联系我。"</text>
  
  <!-- Step 8: 回访与验证 -->
  <rect x="100" y="520" width="1720" height="280" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="620" r="25" fill="#059669"/>
  <text x="180" y="630" class="content-text" text-anchor="middle" fill="white" font-size="24">8</text>
  <text x="230" y="580" class="step-title" fill="#059669">Step 8: 回访与验证</text>
  <text x="230" y="620" class="content-text">确认效果，防止问题复发</text>
  
  <rect x="250" y="650" width="1400" height="120" fill="#DCFCE7" rx="10"/>
  <text x="270" y="680" class="example-text">"王先生，设备更换完成了，</text>
  <text x="270" y="710" class="example-text">问题解决了吗？网络使用还满意吗？</text>
  <text x="270" y="740" class="example-text">如果还有任何问题，请随时联系我。"</text>
  
  <!-- 技巧提示 -->
  <rect x="100" y="820" width="1720" height="140" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="870" r="20" fill="#8B5CF6"/>
  <text x="180" y="880" class="content-text" text-anchor="middle" fill="white" font-size="18">技巧</text>
  <text x="230" y="850" class="step-title" fill="#8B5CF6">关键技巧：</text>
  <g transform="translate(280, 880)">
    <circle cx="0" cy="0" r="8" fill="#8B5CF6"/>
    <text x="20" y="8" class="tip-text">承诺要兑现，说到做到</text>
    
    <circle cx="400" cy="0" r="8" fill="#8B5CF6"/>
    <text x="420" y="8" class="tip-text">回访要及时，确认满意</text>
    
    <circle cx="800" cy="0" r="8" fill="#8B5CF6"/>
    <text x="820" y="8" class="tip-text">建立长期联系</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <!-- 闭环图标 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#059669" stroke-width="4"/>
    <circle cx="0" cy="0" r="50" fill="#059669" opacity="0.1"/>
    
    <!-- 闭环箭头 -->
    <path d="M 0 -60 A 60 60 0 1 1 52 -30" stroke="#059669" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
    
    <text x="0" y="5" class="content-text" text-anchor="middle" font-size="24" fill="#059669">闭环</text>
    <text x="0" y="120" class="content-text" text-anchor="middle" font-size="20" fill="#059669">确认验证</text>
    
    <!-- 完成标识 -->
    <g transform="translate(0, 160)">
      <circle cx="0" cy="0" r="25" fill="#10B981"/>
      <path d="M -10 0 L -5 8 L 12 -8" stroke="white" stroke-width="3" fill="none"/>
      <text x="0" y="35" class="content-text" text-anchor="middle" font-size="16">完成</text>
    </g>
  </g>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#059669"/>
    </marker>
  </defs>
</svg>
