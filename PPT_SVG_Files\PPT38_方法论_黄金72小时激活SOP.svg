<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #DC2626; font-weight: bold; text-anchor: middle; }
      .goal-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; text-anchor: middle; }
      .phase-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; }
      .action-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">方法论：黄金72小时激活SOP</text>
  
  <!-- 目标 -->
  <rect x="200" y="200" width="1520" height="100" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <text x="960" y="240" class="subtitle-text" fill="#059669">目标：</text>
  <text x="960" y="280" class="goal-text">帮助上手、优化体验、激发兴趣、固化行为</text>
  
  <!-- 时间轴展示三个阶段 -->
  <g transform="translate(960, 500)">
    <!-- 时间轴主线 -->
    <line x1="-600" y1="0" x2="600" y2="0" stroke="#6B7280" stroke-width="4"/>
    
    <!-- 阶段一：0-24h -->
    <g transform="translate(-400, 0)">
      <circle cx="0" cy="0" r="40" fill="#DC2626"/>
      <text x="0" y="-10" class="phase-text" text-anchor="middle" fill="white" font-size="16">0-24h</text>
      <text x="0" y="10" class="phase-text" text-anchor="middle" fill="white" font-size="14">认知建立</text>
      
      <rect x="-120" y="80" width="240" height="180" fill="#FEF2F2" stroke="#DC2626" stroke-width="2" rx="15"/>
      <text x="0" y="110" class="phase-text" text-anchor="middle" fill="#DC2626">认知建立</text>
      <text x="0" y="140" class="phase-text" text-anchor="middle" fill="#DC2626">基础保障</text>
      
      <text x="0" y="170" class="action-text" text-anchor="middle">推送新装指南</text>
      <text x="0" y="195" class="action-text" text-anchor="middle">设备绑定APP</text>
      <text x="0" y="220" class="action-text" text-anchor="middle">WiFi快检</text>
      <text x="0" y="245" class="action-text" text-anchor="middle">欢迎消息</text>
    </g>
    
    <!-- 阶段二：24-48h -->
    <g transform="translate(0, 0)">
      <circle cx="0" cy="0" r="40" fill="#059669"/>
      <text x="0" y="-10" class="phase-text" text-anchor="middle" fill="white" font-size="16">24-48h</text>
      <text x="0" y="10" class="phase-text" text-anchor="middle" fill="white" font-size="14">需求激发</text>
      
      <rect x="-120" y="80" width="240" height="180" fill="#F0FDF4" stroke="#059669" stroke-width="2" rx="15"/>
      <text x="0" y="110" class="phase-text" text-anchor="middle" fill="#059669">需求激发</text>
      <text x="0" y="140" class="phase-text" text-anchor="middle" fill="#059669">体验优化</text>
      
      <text x="0" y="170" class="action-text" text-anchor="middle">WiFi诊断报告</text>
      <text x="0" y="195" class="action-text" text-anchor="middle">场景产品推荐</text>
      <text x="0" y="220" class="action-text" text-anchor="middle">游戏加速</text>
      <text x="0" y="245" class="action-text" text-anchor="middle">Mesh推荐</text>
    </g>
    
    <!-- 阶段三：48-72h -->
    <g transform="translate(400, 0)">
      <circle cx="0" cy="0" r="40" fill="#2563EB"/>
      <text x="0" y="-10" class="phase-text" text-anchor="middle" fill="white" font-size="16">48-72h</text>
      <text x="0" y="10" class="phase-text" text-anchor="middle" fill="white" font-size="14">行为固化</text>
      
      <rect x="-120" y="80" width="240" height="180" fill="#EFF6FF" stroke="#2563EB" stroke-width="2" rx="15"/>
      <text x="0" y="110" class="phase-text" text-anchor="middle" fill="#2563EB">行为固化</text>
      <text x="0" y="140" class="phase-text" text-anchor="middle" fill="#2563EB">价值初探</text>
      
      <text x="0" y="170" class="action-text" text-anchor="middle">积分任务</text>
      <text x="0" y="195" class="action-text" text-anchor="middle">首单特惠</text>
      <text x="0" y="220" class="action-text" text-anchor="middle">NPS评分</text>
      <text x="0" y="245" class="action-text" text-anchor="middle">推荐有礼</text>
    </g>
  </g>
  
  <!-- 底部说明 -->
  <rect x="300" y="850" width="1320" height="80" fill="#E0F2FE" stroke="#0284C7" stroke-width="2" rx="15"/>
  <text x="960" y="880" class="goal-text">关键：分阶段、有节奏、个性化</text>
  <text x="960" y="910" class="goal-text">让每个新用户都能快速感知价值</text>
</svg>
