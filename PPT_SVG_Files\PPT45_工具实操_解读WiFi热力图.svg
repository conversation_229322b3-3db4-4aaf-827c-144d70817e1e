<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .color-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; }
      .method-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2563EB; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">工具实操：解读WiFi热力图</text>
  
  <!-- 这是什么 -->
  <rect x="100" y="200" width="1720" height="120" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="240" r="15" fill="#2563EB"/>
  <text x="220" y="230" class="section-title" fill="#2563EB">这是什么：</text>
  <text x="220" y="270" class="content-text">可视化展示WiFi信号强度分布</text>
  <text x="220" y="310" class="content-text">直观了解覆盖情况和问题区域</text>
  
  <!-- 颜色含义 -->
  <rect x="100" y="340" width="1720" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="410" r="15" fill="#059669"/>
  <text x="220" y="390" class="section-title" fill="#059669">颜色含义：</text>
  
  <g transform="translate(280, 420)">
    <!-- 绿色 -->
    <rect x="0" y="0" width="80" height="40" fill="#059669" rx="5"/>
    <text x="40" y="25" class="color-text" text-anchor="middle" fill="white">绿色</text>
    <text x="100" y="25" class="content-text">信号强（-50dBm以上）</text>
    
    <!-- 黄色 -->
    <rect x="400" y="0" width="80" height="40" fill="#D97706" rx="5"/>
    <text x="440" y="25" class="color-text" text-anchor="middle" fill="white">黄色</text>
    <text x="500" y="25" class="content-text">信号一般（-50~-70dBm）</text>
    
    <!-- 红色 -->
    <rect x="800" y="0" width="80" height="40" fill="#DC2626" rx="5"/>
    <text x="840" y="25" class="color-text" text-anchor="middle" fill="white">红色</text>
    <text x="900" y="25" class="content-text">信号弱（-70dBm以下）</text>
  </g>
  
  <!-- 热力图示例 -->
  <g transform="translate(100, 560)">
    <!-- 覆盖良好示例 -->
    <rect x="0" y="0" width="400" height="300" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="200" y="30" class="content-text" text-anchor="middle" font-size="24">覆盖良好</text>
    
    <!-- 房屋轮廓 -->
    <rect x="50" y="50" width="300" height="200" fill="none" stroke="#6B7280" stroke-width="2"/>
    <line x1="200" y1="50" x2="200" y2="250" stroke="#6B7280" stroke-width="1"/>
    <line x1="50" y1="150" x2="350" y2="150" stroke="#6B7280" stroke-width="1"/>
    
    <!-- 绿色覆盖 -->
    <rect x="60" y="60" width="130" height="80" fill="#059669" opacity="0.6"/>
    <rect x="210" y="60" width="130" height="80" fill="#059669" opacity="0.6"/>
    <rect x="60" y="160" width="130" height="80" fill="#059669" opacity="0.6"/>
    <rect x="210" y="160" width="130" height="80" fill="#059669" opacity="0.6"/>
    
    <!-- 路由器位置 -->
    <circle cx="200" cy="150" r="8" fill="#2563EB"/>
    <text x="200" y="280" class="content-text" text-anchor="middle" font-size="18">全屋绿色覆盖</text>
  </g>
  
  <!-- 有盲区示例 -->
  <g transform="translate(550, 560)">
    <rect x="0" y="0" width="400" height="300" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="200" y="30" class="content-text" text-anchor="middle" font-size="24">有盲区</text>
    
    <!-- 房屋轮廓 -->
    <rect x="50" y="50" width="300" height="200" fill="none" stroke="#6B7280" stroke-width="2"/>
    <line x1="200" y1="50" x2="200" y2="250" stroke="#6B7280" stroke-width="1"/>
    <line x1="50" y1="150" x2="350" y2="150" stroke="#6B7280" stroke-width="1"/>
    
    <!-- 混合覆盖 -->
    <rect x="60" y="60" width="130" height="80" fill="#059669" opacity="0.6"/>
    <rect x="210" y="60" width="130" height="80" fill="#D97706" opacity="0.6"/>
    <rect x="60" y="160" width="130" height="80" fill="#D97706" opacity="0.6"/>
    <rect x="210" y="160" width="130" height="80" fill="#DC2626" opacity="0.6"/>
    
    <!-- 路由器位置 -->
    <circle cx="125" cy="125" r="8" fill="#2563EB"/>
    <text x="200" y="280" class="content-text" text-anchor="middle" font-size="18">存在信号盲区</text>
  </g>
  
  <!-- 如何获取 -->
  <rect x="1000" y="560" width="720" height="300" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="1070" cy="610" r="15" fill="#D97706"/>
  <text x="1110" y="590" class="section-title" fill="#D97706">如何获取：</text>
  <text x="1110" y="640" class="method-text">• 爱家平台远程生成</text>
  <text x="1110" y="680" class="method-text">• APP现场测量上传</text>
  <text x="1110" y="720" class="method-text">• 专业测试工具扫描</text>
  
  <text x="1110" y="780" class="content-text">实时监测，动态更新</text>
  <text x="1110" y="820" class="content-text">为优化方案提供数据支撑</text>
</svg>
