<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .value-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #DC2626; font-weight: bold; }
      .demo-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #8B5CF6; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">工具应用：AR场景营销（复习）</text>
  
  <!-- 价值 -->
  <rect x="100" y="240" width="1720" height="200" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="320" r="20" fill="#DC2626"/>
  <text x="180" y="330" class="content-text" text-anchor="middle" fill="white" font-size="20">价值</text>
  <text x="230" y="300" class="section-title" fill="#DC2626">核心价值：</text>
  <text x="230" y="350" class="value-text">让客户"眼见为实"</text>
  <text x="230" y="390" class="value-text">提升购买意愿</text>
  <text x="230" y="430" class="content-text">通过虚拟现实技术展示产品效果</text>
  
  <!-- 展示内容 -->
  <rect x="100" y="460" width="1720" height="200" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="540" r="20" fill="#8B5CF6"/>
  <text x="180" y="550" class="content-text" text-anchor="middle" fill="white" font-size="18">展示</text>
  <text x="230" y="520" class="section-title" fill="#8B5CF6">展示内容：</text>
  <text x="230" y="570" class="demo-text">截图/视频：展示AR眼镜或手机APP</text>
  <text x="230" y="610" class="demo-text">模拟智能家居效果</text>
  <text x="230" y="650" class="content-text">让客户直观感受产品在家中的效果</text>
  
  <!-- 应用场景 -->
  <rect x="100" y="680" width="1720" height="240" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="770" r="20" fill="#059669"/>
  <text x="180" y="780" class="content-text" text-anchor="middle" fill="white" font-size="18">场景</text>
  <text x="230" y="750" class="section-title" fill="#059669">应用场景：</text>
  <g transform="translate(280, 790)">
    <circle cx="0" cy="0" r="10" fill="#059669"/>
    <text x="25" y="8" class="content-text">智能家居产品展示</text>
    
    <circle cx="0" cy="50" r="10" fill="#059669"/>
    <text x="25" y="58" class="content-text">家庭网络覆盖效果演示</text>
    
    <circle cx="0" cy="100" r="10" fill="#059669"/>
    <text x="25" y="108" class="content-text">安防监控布局规划</text>
  </g>
  
  <!-- 装饰元素 - AR展示图 -->
  <g transform="translate(1500, 500)">
    <!-- AR设备框架 -->
    <rect x="0" y="0" width="300" height="250" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="150" y="30" class="content-text" text-anchor="middle" font-size="24">AR场景展示</text>
    
    <!-- 虚拟场景 -->
    <g transform="translate(50, 60)">
      <!-- 房间轮廓 -->
      <rect x="0" y="0" width="200" height="120" fill="none" stroke="#6B7280" stroke-width="2" rx="5"/>
      
      <!-- 虚拟设备 -->
      <circle cx="50" cy="30" r="15" fill="#2563EB" opacity="0.6"/>
      <text x="50" y="35" class="content-text" text-anchor="middle" fill="white" font-size="12">路由</text>
      
      <rect x="120" y="20" width="30" height="20" fill="#059669" opacity="0.6" rx="3"/>
      <text x="135" y="33" class="content-text" text-anchor="middle" fill="white" font-size="10">摄像头</text>
      
      <circle cx="160" cy="80" r="12" fill="#D97706" opacity="0.6"/>
      <text x="160" y="85" class="content-text" text-anchor="middle" fill="white" font-size="10">音箱</text>
      
      <!-- WiFi覆盖范围 -->
      <circle cx="50" cy="30" r="40" fill="#2563EB" opacity="0.2"/>
      <circle cx="50" cy="30" r="60" fill="#2563EB" opacity="0.1"/>
    </g>
    
    <text x="150" y="200" class="content-text" text-anchor="middle" font-size="20">虚拟体验</text>
    <text x="150" y="225" class="content-text" text-anchor="middle" font-size="18">眼见为实</text>
  </g>
</svg>
