<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #DC2626; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .method-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #2563EB; font-weight: bold; }
      .item-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">六必查 6/6：工具物料清点</text>
  
  <!-- 查什么 -->
  <rect x="100" y="220" width="1720" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="15" fill="#059669"/>
  <text x="220" y="270" class="section-title">查什么：</text>
  <text x="220" y="320" class="content-text">光猫、路由、网线、工具包、测速手机等</text>
  <text x="220" y="370" class="content-text">是否齐全、完好？</text>
  
  <!-- 为何查 -->
  <rect x="100" y="440" width="1720" height="160" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="500" r="15" fill="#2563EB"/>
  <text x="220" y="480" class="section-title">为何查：</text>
  <text x="220" y="530" class="content-text">避免到场后缺这少那，耽误时间</text>
  <text x="220" y="570" class="content-text">确保一次性完成安装</text>
  
  <!-- 怎么查 -->
  <rect x="100" y="620" width="1720" height="160" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="680" r="15" fill="#D97706"/>
  <text x="220" y="660" class="section-title">怎么查：</text>
  <text x="220" y="710" class="method-text">出发前对照标准清单逐一检查</text>
  <text x="220" y="750" class="method-text">确认设备功能正常</text>
  
  <!-- 装饰元素 - 工具清单 -->
  <g transform="translate(1200, 300)">
    <!-- 清单背景 -->
    <rect x="0" y="0" width="600" height="500" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="3" rx="15"/>
    <text x="300" y="40" class="content-text" text-anchor="middle" font-size="28">标准工具清单</text>
    
    <!-- 清单项目 -->
    <g transform="translate(30, 70)">
      <!-- 设备类 -->
      <text x="0" y="0" class="item-text" font-weight="bold">设备类：</text>
      <circle cx="0" cy="30" r="8" fill="#059669"/>
      <text x="20" y="38" class="item-text">光猫设备</text>
      <circle cx="0" cy="60" r="8" fill="#059669"/>
      <text x="20" y="68" class="item-text">WiFi路由器</text>
      <circle cx="0" cy="90" r="8" fill="#059669"/>
      <text x="20" y="98" class="item-text">网线（多根）</text>
      
      <!-- 工具类 -->
      <text x="0" y="140" class="item-text" font-weight="bold">工具类：</text>
      <circle cx="0" cy="170" r="8" fill="#2563EB"/>
      <text x="20" y="178" class="item-text">光功率计</text>
      <circle cx="0" cy="200" r="8" fill="#2563EB"/>
      <text x="20" y="208" class="item-text">网线钳</text>
      <circle cx="0" cy="230" r="8" fill="#2563EB"/>
      <text x="20" y="238" class="item-text">螺丝刀套装</text>
      <circle cx="0" cy="260" r="8" fill="#2563EB"/>
      <text x="20" y="268" class="item-text">电钻</text>
      
      <!-- 测试类 -->
      <text x="0" y="310" class="item-text" font-weight="bold">测试类：</text>
      <circle cx="0" cy="340" r="8" fill="#D97706"/>
      <text x="20" y="348" class="item-text">测速手机</text>
      <circle cx="0" cy="370" r="8" fill="#D97706"/>
      <text x="20" y="378" class="item-text">AR预勘APP</text>
      <circle cx="0" cy="400" r="8" fill="#D97706"/>
      <text x="20" y="408" class="item-text">爱家平台账号</text>
    </g>
  </g>
  
  <!-- 底部提示 -->
  <rect x="200" y="820" width="1520" height="100" fill="#E0F2FE" stroke="#0284C7" stroke-width="2" rx="15"/>
  <text x="960" y="860" class="content-text" text-anchor="middle">提示：建立标准化工具包，每次出发前5分钟快速检查</text>
  <text x="960" y="900" class="content-text" text-anchor="middle">避免遗漏，提升工作效率</text>
</svg>
