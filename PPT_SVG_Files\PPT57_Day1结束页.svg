<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 72px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #059669; font-weight: bold; text-anchor: middle; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #374151; text-anchor: middle; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #DC2626; font-weight: bold; text-anchor: middle; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 主标题 -->
  <text x="960" y="300" class="title-text">Day 1 结束</text>
  
  <!-- 感谢语 -->
  <text x="960" y="420" class="subtitle-text">感谢参与，收获满满！</text>
  
  <!-- 今日回顾 -->
  <rect x="200" y="480" width="1520" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="30"/>
  <text x="960" y="540" class="content-text">今日学习内容回顾：</text>
  <text x="960" y="590" class="content-text">✓ 入网质量筑基 - 六必查标准化流程</text>
  <text x="960" y="640" class="content-text">✓ 激活与活性管理 - 黄金72小时SOP</text>
  
  <!-- 明日预告 -->
  <rect x="200" y="700" width="1520" height="180" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="30"/>
  <text x="960" y="760" class="highlight-text">明日精彩继续！</text>
  <text x="960" y="810" class="content-text">Day 2 重点：异常处置与挽留</text>
  <text x="960" y="850" class="content-text">流失预警、快速响应、有效挽留</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(300, 600)">
    <!-- Day 1 完成标识 -->
    <circle cx="0" cy="0" r="80" fill="#059669" opacity="0.2"/>
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="32" fill="#059669">Day 1</text>
    <text x="0" y="25" class="content-text" text-anchor="middle" font-size="28" fill="#059669">完成</text>
    <circle cx="0" cy="0" r="50" fill="#059669" opacity="0.4"/>
  </g>
  
  <g transform="translate(1620, 600)">
    <!-- Day 2 预告标识 -->
    <circle cx="0" cy="0" r="80" fill="#2563EB" opacity="0.2"/>
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="32" fill="#2563EB">Day 2</text>
    <text x="0" y="25" class="content-text" text-anchor="middle" font-size="28" fill="#2563EB">预告</text>
    <circle cx="0" cy="0" r="50" fill="#2563EB" opacity="0.4"/>
  </g>
  
  <!-- 箭头指向明天 -->
  <path d="M 400 600 L 1500 600" stroke="#DC2626" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.6"/>
  
  <!-- 底部装饰 -->
  <circle cx="960" cy="950" r="20" fill="#059669" opacity="0.3"/>
  <circle cx="920" cy="970" r="12" fill="#2563EB" opacity="0.4"/>
  <circle cx="1000" cy="970" r="12" fill="#D97706" opacity="0.4"/>
  
  <defs>
    <marker id="arrowhead" markerWidth="15" markerHeight="10" refX="14" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#DC2626"/>
    </marker>
  </defs>
</svg>
