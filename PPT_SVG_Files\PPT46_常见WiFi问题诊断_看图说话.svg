<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .scenario-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #374151; }
      .reason-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #DC2626; }
      .solution-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #059669; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">常见WiFi问题诊断（看图说话）</text>
  
  <!-- 场景1：卧室信号红区 -->
  <rect x="80" y="200" width="1760" height="220" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="150" cy="270" r="20" fill="#DC2626"/>
  <text x="150" y="280" class="content-text" text-anchor="middle" fill="white" font-size="20">1</text>
  <text x="200" y="250" class="scenario-title" fill="#DC2626">场景1：卧室信号红区</text>
  
  <g transform="translate(200, 280)">
    <text x="0" y="0" class="reason-text">原因：距离远/墙体阻挡</text>
    <text x="0" y="30" class="solution-text">建议：调整路由位置/加AP(Mesh)</text>
    
    <!-- 简化热力图 -->
    <g transform="translate(600, -30)">
      <rect x="0" y="0" width="200" height="120" fill="none" stroke="#6B7280" stroke-width="2"/>
      <rect x="10" y="10" width="80" height="50" fill="#059669" opacity="0.6"/>
      <rect x="110" y="10" width="80" height="50" fill="#DC2626" opacity="0.6"/>
      <rect x="10" y="70" width="80" height="40" fill="#D97706" opacity="0.6"/>
      <rect x="110" y="70" width="80" height="40" fill="#DC2626" opacity="0.6"/>
      <circle cx="50" cy="35" r="5" fill="#2563EB"/>
      <text x="100" y="140" class="content-text" text-anchor="middle" font-size="18">卧室信号弱</text>
    </g>
  </g>
  
  <!-- 场景2：全屋信号普遍黄/红 -->
  <rect x="80" y="440" width="1760" height="220" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="150" cy="510" r="20" fill="#D97706"/>
  <text x="150" y="520" class="content-text" text-anchor="middle" fill="white" font-size="20">2</text>
  <text x="200" y="490" class="scenario-title" fill="#D97706">场景2：全屋信号普遍黄/红</text>
  
  <g transform="translate(200, 520)">
    <text x="0" y="0" class="reason-text">原因：路由器性能差/位置不佳</text>
    <text x="0" y="30" class="solution-text">建议：更换高性能路由/优化位置</text>
    
    <!-- 简化热力图 -->
    <g transform="translate(600, -30)">
      <rect x="0" y="0" width="200" height="120" fill="none" stroke="#6B7280" stroke-width="2"/>
      <rect x="10" y="10" width="80" height="50" fill="#D97706" opacity="0.6"/>
      <rect x="110" y="10" width="80" height="50" fill="#DC2626" opacity="0.6"/>
      <rect x="10" y="70" width="80" height="40" fill="#D97706" opacity="0.6"/>
      <rect x="110" y="70" width="80" height="40" fill="#DC2626" opacity="0.6"/>
      <circle cx="50" cy="35" r="5" fill="#2563EB"/>
      <text x="100" y="140" class="content-text" text-anchor="middle" font-size="18">整体信号差</text>
    </g>
  </g>
  
  <!-- 场景3：信号强但卡顿 -->
  <rect x="80" y="680" width="1760" height="220" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="150" cy="750" r="20" fill="#2563EB"/>
  <text x="150" y="760" class="content-text" text-anchor="middle" fill="white" font-size="20">3</text>
  <text x="200" y="730" class="scenario-title" fill="#2563EB">场景3：信号强但卡顿</text>
  
  <g transform="translate(200, 760)">
    <text x="0" y="0" class="reason-text">原因：干扰严重/设备连接数过多</text>
    <text x="0" y="30" class="solution-text">建议：修改信道/升级路由</text>
    
    <!-- 干扰检测图 -->
    <g transform="translate(600, -30)">
      <rect x="0" y="0" width="200" height="120" fill="none" stroke="#6B7280" stroke-width="2"/>
      <rect x="10" y="10" width="80" height="50" fill="#059669" opacity="0.6"/>
      <rect x="110" y="10" width="80" height="50" fill="#059669" opacity="0.6"/>
      <rect x="10" y="70" width="80" height="40" fill="#059669" opacity="0.6"/>
      <rect x="110" y="70" width="80" height="40" fill="#059669" opacity="0.6"/>
      
      <!-- 干扰波纹 -->
      <circle cx="100" cy="60" r="30" fill="none" stroke="#DC2626" stroke-width="2" opacity="0.7"/>
      <circle cx="100" cy="60" r="50" fill="none" stroke="#DC2626" stroke-width="2" opacity="0.5"/>
      
      <text x="100" y="140" class="content-text" text-anchor="middle" font-size="18">信号强但干扰</text>
    </g>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <circle cx="0" cy="0" r="80" fill="none" stroke="#3B82F6" stroke-width="3" opacity="0.3"/>
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="24">诊断</text>
    <text x="0" y="15" class="content-text" text-anchor="middle" font-size="24">分析</text>
    
    <!-- 诊断工具图标 -->
    <rect x="-30" y="30" width="60" height="40" fill="#2563EB" opacity="0.2" rx="5"/>
    <text x="0" y="55" class="content-text" text-anchor="middle" font-size="18">热力图</text>
  </g>
</svg>
