<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .tool-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; }
      .item-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">挽留工具箱简介</text>
  
  <!-- 蜂鸟挽留决策引擎 -->
  <rect x="100" y="220" width="1720" height="180" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="20" fill="#2563EB"/>
  <text x="180" y="300" class="content-text" text-anchor="middle" fill="white" font-size="18">引擎</text>
  <text x="230" y="270" class="section-title" fill="#2563EB">蜂鸟挽留决策引擎：</text>
  <text x="230" y="310" class="tool-text" fill="#2563EB">智能推荐挽留策略</text>
  <text x="230" y="350" class="content-text">基于客户画像和历史数据，自动匹配最优方案</text>
  <text x="230" y="380" class="content-text">截图示意：展示策略推荐界面</text>
  
  <!-- 补偿资源池 -->
  <rect x="100" y="420" width="1720" height="280" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="520" r="20" fill="#059669"/>
  <text x="180" y="530" class="content-text" text-anchor="middle" fill="white" font-size="18">资源</text>
  <text x="230" y="480" class="section-title" fill="#059669">补偿资源池：</text>
  <text x="230" y="520" class="tool-text" fill="#059669">丰富的挽留资源库</text>
  
  <g transform="translate(280, 560)">
    <circle cx="0" cy="0" r="8" fill="#059669"/>
    <text x="20" y="8" class="item-text">话费券、流量包</text>
    
    <circle cx="300" cy="0" r="8" fill="#059669"/>
    <text x="320" y="8" class="item-text">VIP服务、加速包</text>
    
    <circle cx="600" cy="0" r="8" fill="#059669"/>
    <text x="620" y="8" class="item-text">积分奖励</text>
    
    <circle cx="0" cy="40" r="8" fill="#059669"/>
    <text x="20" y="48" class="item-text">智能设备优惠券</text>
    
    <circle cx="300" cy="40" r="8" fill="#059669"/>
    <text x="320" y="48" class="item-text">专属礼品</text>
    
    <circle cx="600" cy="40" r="8" fill="#059669"/>
    <text x="620" y="48" class="item-text">免费升级服务</text>
    
    <circle cx="0" cy="80" r="8" fill="#059669"/>
    <text x="20" y="88" class="content-text">根据客户价值等级，灵活组合使用</text>
  </g>
  
  <!-- 竞品策略库 -->
  <rect x="100" y="720" width="1720" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="800" r="20" fill="#D97706"/>
  <text x="180" y="810" class="content-text" text-anchor="middle" fill="white" font-size="18">策略</text>
  <text x="230" y="780" class="section-title" fill="#D97706">竞品策略库：</text>
  <text x="230" y="820" class="tool-text" fill="#D97706">了解对手，知己知彼</text>
  <text x="230" y="860" class="content-text">实时更新竞品价格、优惠、服务信息</text>
  <text x="230" y="900" class="content-text">强调：及时更新，保持信息准确性</text>
  
  <!-- 装饰元素 - 工具箱 -->
  <g transform="translate(1500, 500)">
    <!-- 工具箱主体 -->
    <rect x="0" y="0" width="300" height="200" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="3" rx="15"/>
    <text x="150" y="30" class="content-text" text-anchor="middle" font-size="24">挽留工具箱</text>
    
    <!-- 工具图标 -->
    <g transform="translate(30, 60)">
      <!-- 决策引擎 -->
      <rect x="0" y="0" width="80" height="40" fill="#2563EB" opacity="0.6" rx="5"/>
      <text x="40" y="25" class="content-text" text-anchor="middle" fill="white" font-size="16">引擎</text>
      
      <!-- 资源池 -->
      <rect x="100" y="0" width="80" height="40" fill="#059669" opacity="0.6" rx="5"/>
      <text x="140" y="25" class="content-text" text-anchor="middle" fill="white" font-size="16">资源</text>
      
      <!-- 策略库 -->
      <rect x="200" y="0" width="80" height="40" fill="#D97706" opacity="0.6" rx="5"/>
      <text x="240" y="25" class="content-text" text-anchor="middle" fill="white" font-size="16">策略</text>
      
      <!-- 数据分析 -->
      <rect x="0" y="60" width="80" height="40" fill="#8B5CF6" opacity="0.6" rx="5"/>
      <text x="40" y="85" class="content-text" text-anchor="middle" fill="white" font-size="16">分析</text>
      
      <!-- 效果跟踪 -->
      <rect x="100" y="60" width="80" height="40" fill="#DC2626" opacity="0.6" rx="5"/>
      <text x="140" y="85" class="content-text" text-anchor="middle" fill="white" font-size="16">跟踪</text>
      
      <!-- 优化建议 -->
      <rect x="200" y="60" width="80" height="40" fill="#6B7280" opacity="0.6" rx="5"/>
      <text x="240" y="85" class="content-text" text-anchor="middle" fill="white" font-size="16">优化</text>
    </g>
    
    <text x="150" y="180" class="content-text" text-anchor="middle" font-size="20">智能挽留</text>
  </g>
</svg>
