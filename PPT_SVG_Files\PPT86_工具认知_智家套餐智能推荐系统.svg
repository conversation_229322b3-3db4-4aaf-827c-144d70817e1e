<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #374151; }
      .logic-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #8B5CF6; }
      .application-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #059669; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">工具认知：智家套餐智能推荐系统</text>
  
  <!-- 推荐逻辑简介 -->
  <rect x="100" y="200" width="1720" height="200" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="270" r="20" fill="#8B5CF6"/>
  <text x="180" y="280" class="content-text" text-anchor="middle" fill="white" font-size="18">逻辑</text>
  <text x="230" y="250" class="section-title" fill="#8B5CF6">推荐逻辑简介：</text>
  <g transform="translate(280, 280)">
    <circle cx="0" cy="0" r="8" fill="#8B5CF6"/>
    <text x="20" y="8" class="logic-text">协同过滤：相似用户喜欢的产品</text>
    
    <circle cx="0" cy="40" r="8" fill="#8B5CF6"/>
    <text x="20" y="48" class="logic-text">内容推荐：基于产品特征匹配</text>
    
    <circle cx="0" cy="80" r="8" fill="#8B5CF6"/>
    <text x="20" y="88" class="logic-text">画像推荐：基于用户标签特征</text>
    
    <circle cx="0" cy="120" r="8" fill="#8B5CF6"/>
    <text x="20" y="128" class="logic-text">实时意图：基于当前行为分析</text>
  </g>
  
  <!-- 截图展示 -->
  <rect x="100" y="420" width="1720" height="120" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="460" r="20" fill="#2563EB"/>
  <text x="180" y="470" class="content-text" text-anchor="middle" fill="white" font-size="18">界面</text>
  <text x="230" y="450" class="section-title" fill="#2563EB">系统界面：</text>
  <text x="230" y="490" class="content-text">截图：展示推荐策略配置界面（简化）</text>
  <text x="230" y="520" class="content-text">包含推荐算法、权重设置、效果监控等</text>
  
  <!-- 一线应用 -->
  <rect x="100" y="560" width="1720" height="320" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="670" r="20" fill="#059669"/>
  <text x="180" y="680" class="content-text" text-anchor="middle" fill="white" font-size="18">应用</text>
  <text x="230" y="630" class="section-title" fill="#059669">一线应用：</text>
  
  <g transform="translate(280, 670)">
    <circle cx="0" cy="0" r="10" fill="#059669"/>
    <text x="25" y="8" class="application-text">了解系统可能给客户推荐了什么</text>
    <text x="50" y="38" class="content-text" font-size="22">便于协同营销，避免重复推荐</text>
    
    <circle cx="0" cy="80" r="10" fill="#059669"/>
    <text x="25" y="88" class="application-text">根据系统提供的"推荐理由"与客户沟通</text>
    <text x="50" y="118" class="content-text" font-size="22">增强说服力，提升转化率</text>
    
    <circle cx="0" cy="160" r="10" fill="#059669"/>
    <text x="25" y="168" class="application-text">向后台反馈推荐效果和用户偏好</text>
    <text x="50" y="198" class="content-text" font-size="22">帮助优化模型，提升推荐精度</text>
  </g>
  
  <!-- 装饰元素 - 推荐系统架构图 -->
  <g transform="translate(1400, 500)">
    <!-- 系统架构 -->
    <rect x="0" y="0" width="400" height="350" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="200" y="30" class="content-text" text-anchor="middle" font-size="24">智家推荐系统</text>
    
    <!-- 数据输入层 -->
    <rect x="30" y="60" width="340" height="50" fill="#DBEAFE" rx="10"/>
    <text x="200" y="80" class="content-text" text-anchor="middle" font-size="18" fill="#2563EB">数据输入层</text>
    <text x="200" y="100" class="content-text" text-anchor="middle" font-size="14">用户画像+行为数据+产品信息</text>
    
    <!-- 算法处理层 -->
    <rect x="30" y="130" width="340" height="50" fill="#F3E8FF" rx="10"/>
    <text x="200" y="150" class="content-text" text-anchor="middle" font-size="18" fill="#8B5CF6">算法处理层</text>
    <text x="200" y="170" class="content-text" text-anchor="middle" font-size="14">协同过滤+内容推荐+深度学习</text>
    
    <!-- 策略决策层 -->
    <rect x="30" y="200" width="340" height="50" fill="#DCFCE7" rx="10"/>
    <text x="200" y="220" class="content-text" text-anchor="middle" font-size="18" fill="#059669">策略决策层</text>
    <text x="200" y="240" class="content-text" text-anchor="middle" font-size="14">业务规则+营销策略+风控约束</text>
    
    <!-- 推荐输出层 -->
    <rect x="30" y="270" width="340" height="50" fill="#FEF3C7" rx="10"/>
    <text x="200" y="290" class="content-text" text-anchor="middle" font-size="18" fill="#D97706">推荐输出层</text>
    <text x="200" y="310" class="content-text" text-anchor="middle" font-size="14">个性化推荐+推荐理由+置信度</text>
    
    <text x="200" y="340" class="content-text" text-anchor="middle" font-size="20">智能推荐</text>
  </g>
</svg>
