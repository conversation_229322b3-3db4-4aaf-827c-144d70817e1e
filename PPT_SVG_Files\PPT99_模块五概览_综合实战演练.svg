<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #DC2626; font-weight: bold; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #059669; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 模块五标题 -->
  <text x="960" y="220" class="title-text">模块五概览</text>
  <text x="960" y="300" class="subtitle-text">综合实战演练 - 学以致用，制定行动！</text>
  
  <!-- 核心内容 -->
  <rect x="150" y="340" width="1620" height="140" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="220" cy="380" r="15" fill="#DC2626"/>
  <text x="270" y="370" class="section-title" fill="#DC2626">核心内容：</text>
  <text x="270" y="410" class="content-text">标杆学习、问题诊断、行动计划</text>
  <text x="270" y="450" class="content-text">将理论转化为实践，制定具体行动方案</text>
  
  <!-- 学习目标 -->
  <rect x="150" y="500" width="1620" height="240" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="220" cy="580" r="15" fill="#059669"/>
  <text x="270" y="560" class="section-title">学习目标：</text>
  <text x="320" y="600" class="content-text">• 借鉴经验：学习标杆案例的成功做法</text>
  <text x="320" y="640" class="content-text">• 诊断问题：识别自身工作中的关键问题</text>
  <text x="320" y="680" class="content-text">• 输出方案：制定90天改进行动计划</text>
  <text x="320" y="720" class="content-text">• 付诸行动：明确责任人和时间节点</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 600)">
    <!-- 实战演练图标 -->
    <circle cx="0" cy="0" r="100" fill="none" stroke="#DC2626" stroke-width="4"/>
    <circle cx="0" cy="0" r="80" fill="#DC2626" opacity="0.1"/>
    
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="32" fill="#DC2626">实战</text>
    <text x="0" y="25" class="content-text" text-anchor="middle" font-size="32" fill="#DC2626">演练</text>
    
    <!-- 行动计划图标 -->
    <g transform="translate(0, 140)">
      <rect x="-40" y="-20" width="80" height="40" fill="#059669" opacity="0.2" rx="20"/>
      <text x="0" y="5" class="content-text" text-anchor="middle" font-size="18" fill="#059669">行动计划</text>
    </g>
  </g>
  
  <!-- 底部强调 -->
  <rect x="200" y="760" width="1520" height="120" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <text x="960" y="800" class="content-text" text-anchor="middle" fill="#2563EB">关键转变：从学习到行动</text>
  <text x="960" y="840" class="content-text" text-anchor="middle">将所学知识转化为具体的改进措施</text>
  <text x="960" y="870" class="content-text" text-anchor="middle">确保培训效果在实际工作中落地</text>
</svg>
