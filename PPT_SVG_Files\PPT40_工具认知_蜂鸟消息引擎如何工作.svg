<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #DC2626; font-weight: bold; }
      .flow-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">工具认知：蜂鸟消息引擎如何工作？</text>
  
  <!-- 原理 -->
  <rect x="100" y="200" width="1720" height="140" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="250" r="15" fill="#2563EB"/>
  <text x="220" y="230" class="section-title" fill="#2563EB">工作原理：</text>
  <text x="220" y="270" class="content-text">根据"用户激活成功"等事件，自动触发</text>
  <text x="220" y="310" class="content-text">预设好的消息推送流程</text>
  
  <!-- 流程图 -->
  <g transform="translate(960, 500)">
    <!-- 事件触发 -->
    <g transform="translate(-400, 0)">
      <rect x="-80" y="-40" width="160" height="80" fill="#FEF2F2" stroke="#DC2626" stroke-width="2" rx="15"/>
      <text x="0" y="-10" class="flow-text" text-anchor="middle" fill="#DC2626">用户事件</text>
      <text x="0" y="15" class="flow-text" text-anchor="middle" fill="#DC2626">激活成功</text>
    </g>
    
    <!-- 箭头1 -->
    <path d="M -240 0 L -160 0" stroke="#6B7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 引擎处理 -->
    <g transform="translate(0, 0)">
      <rect x="-80" y="-40" width="160" height="80" fill="#EFF6FF" stroke="#2563EB" stroke-width="2" rx="15"/>
      <text x="0" y="-10" class="flow-text" text-anchor="middle" fill="#2563EB">蜂鸟引擎</text>
      <text x="0" y="15" class="flow-text" text-anchor="middle" fill="#2563EB">智能匹配</text>
    </g>
    
    <!-- 箭头2 -->
    <path d="M 80 0 L 160 0" stroke="#6B7280" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- 消息推送 -->
    <g transform="translate(400, 0)">
      <rect x="-80" y="-40" width="160" height="80" fill="#F0FDF4" stroke="#059669" stroke-width="2" rx="15"/>
      <text x="0" y="-10" class="flow-text" text-anchor="middle" fill="#059669">自动推送</text>
      <text x="0" y="15" class="flow-text" text-anchor="middle" fill="#059669">个性化消息</text>
    </g>
  </g>
  
  <!-- 配置界面示意 -->
  <rect x="100" y="600" width="800" height="300" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
  <text x="500" y="640" class="content-text" text-anchor="middle" font-size="28">自动化任务流配置界面（简化示意）</text>
  
  <!-- 配置示例 -->
  <g transform="translate(150, 680)">
    <rect x="0" y="0" width="700" height="40" fill="#E5E7EB" rx="5"/>
    <text x="20" y="25" class="flow-text">触发条件：用户激活成功 → 延迟1小时 → 推送欢迎消息</text>
    
    <rect x="0" y="50" width="700" height="40" fill="#E5E7EB" rx="5"/>
    <text x="20" y="75" class="flow-text">触发条件：设备未绑定 → 延迟6小时 → 推送绑定提醒</text>
    
    <rect x="0" y="100" width="700" height="40" fill="#E5E7EB" rx="5"/>
    <text x="20" y="125" class="flow-text">触发条件：WiFi异常 → 立即推送 → 诊断报告</text>
    
    <rect x="0" y="150" width="700" height="40" fill="#E5E7EB" rx="5"/>
    <text x="20" y="175" class="flow-text">触发条件：24小时无活动 → 推送使用指南</text>
  </g>
  
  <!-- 对一线意义 -->
  <rect x="950" y="600" width="870" height="300" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="1020" cy="650" r="15" fill="#D97706"/>
  <text x="1060" y="630" class="section-title" fill="#D97706">对一线意义：</text>
  <text x="1060" y="680" class="content-text">了解客户会收到哪些信息</text>
  <text x="1060" y="720" class="content-text">便于解答疑问或协同服务</text>
  <text x="1060" y="780" class="highlight-text">主动告知 > 被动询问</text>
  <text x="1060" y="820" class="content-text">提升服务专业性和</text>
  <text x="1060" y="860" class="content-text">客户满意度</text>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6B7280"/>
    </marker>
  </defs>
</svg>
