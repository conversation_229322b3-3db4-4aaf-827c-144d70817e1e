<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #DC2626; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .method-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #2563EB; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">六必查 4/6：设备兼容确认</text>
  
  <!-- 查什么 -->
  <rect x="100" y="220" width="1720" height="220" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="300" r="15" fill="#059669"/>
  <text x="220" y="280" class="section-title">查什么：</text>
  <text x="220" y="330" class="content-text">• 光猫型号是否匹配业务速率/类型？</text>
  <text x="220" y="380" class="content-text">• 设备版本是否为最新？</text>
  <text x="220" y="430" class="content-text">• 端口数量是否满足需求？</text>
  
  <!-- 为何查 -->
  <rect x="100" y="460" width="1720" height="180" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="530" r="15" fill="#2563EB"/>
  <text x="220" y="510" class="section-title">为何查：</text>
  <text x="220" y="560" class="content-text">设备不匹配导致业务无法开通</text>
  <text x="220" y="610" class="content-text">或性能打折，影响客户体验</text>
  
  <!-- 怎么查 -->
  <rect x="100" y="660" width="1720" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="730" r="15" fill="#D97706"/>
  <text x="220" y="710" class="section-title">怎么查：</text>
  <text x="220" y="760" class="method-text">• 核对工单业务信息</text>
  <text x="220" y="800" class="method-text">• 检查设备清单</text>
  <text x="220" y="840" class="method-text">• 查看光猫规格说明</text>
  
  <!-- 装饰元素 - 设备示意图 -->
  <g transform="translate(1400, 350)">
    <!-- 光猫 -->
    <rect x="0" y="0" width="300" height="120" fill="#E5E7EB" stroke="#6B7280" stroke-width="3" rx="10"/>
    <text x="150" y="30" class="content-text" text-anchor="middle" font-size="24">光猫设备</text>
    
    <!-- 端口 -->
    <circle cx="50" cy="80" r="12" fill="#059669"/>
    <circle cx="100" cy="80" r="12" fill="#059669"/>
    <circle cx="150" cy="80" r="12" fill="#059669"/>
    <circle cx="200" cy="80" r="12" fill="#059669"/>
    <circle cx="250" cy="80" r="12" fill="#6B7280"/>
    
    <!-- 标签 -->
    <text x="150" y="150" class="content-text" text-anchor="middle" font-size="20">千兆光猫</text>
    <text x="150" y="180" class="content-text" text-anchor="middle" font-size="18">4个千兆端口</text>
    
    <!-- 检查清单 -->
    <rect x="0" y="220" width="300" height="180" fill="#F0FDF4" stroke="#059669" stroke-width="2" rx="10"/>
    <text x="150" y="250" class="content-text" text-anchor="middle" font-size="20">检查清单</text>
    <text x="20" y="280" class="content-text" font-size="18">✓ 速率匹配</text>
    <text x="20" y="310" class="content-text" font-size="18">✓ 版本最新</text>
    <text x="20" y="340" class="content-text" font-size="18">✓ 端口充足</text>
    <text x="20" y="370" class="content-text" font-size="18">✓ 功能完整</text>
  </g>
</svg>
