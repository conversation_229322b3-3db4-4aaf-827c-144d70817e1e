<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 72px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #374151; text-anchor: middle; }
      .info-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #6B7280; text-anchor: middle; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 200 200 Q 960 100 1720 200" class="accent-curve" opacity="0.3"/>
  <path d="M 200 880 Q 960 980 1720 880" class="accent-curve" opacity="0.3"/>
  
  <!-- 主标题 -->
  <text x="960" y="380" class="title-text">家宽全流程精益运营实战</text>
  
  <!-- 副标题 -->
  <text x="960" y="460" class="subtitle-text">从入网到促活的全周期管理能力提升</text>
  <text x="960" y="520" class="subtitle-text">（四川移动定制版）</text>
  
  <!-- 装饰元素 -->
  <circle cx="400" cy="300" r="8" fill="#3B82F6" opacity="0.6"/>
  <circle cx="1520" cy="300" r="8" fill="#3B82F6" opacity="0.6"/>
  <circle cx="400" cy="780" r="8" fill="#3B82F6" opacity="0.6"/>
  <circle cx="1520" cy="780" r="8" fill="#3B82F6" opacity="0.6"/>
  
  <!-- Logo区域 -->
  <rect x="200" y="650" width="200" height="80" fill="#E5E7EB" rx="10"/>
  <text x="300" y="700" class="info-text" font-size="24">四川移动Logo</text>
  
  <rect x="1520" y="650" width="200" height="80" fill="#E5E7EB" rx="10"/>
  <text x="1620" y="700" class="info-text" font-size="24">培训机构Logo</text>
  
  <!-- 讲师信息 -->
  <text x="960" y="800" class="info-text">讲师团队</text>
  <text x="960" y="850" class="info-text">2024年8月</text>
</svg>
