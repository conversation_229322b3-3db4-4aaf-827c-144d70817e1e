<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .scenario-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #DC2626; font-weight: bold; }
      .task-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #059669; }
      .guide-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #8B5CF6; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">沙盘推演：高价值用户流失危机</text>
  
  <!-- 场景回顾 -->
  <rect x="100" y="220" width="1720" height="200" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="20" fill="#DC2626"/>
  <text x="180" y="300" class="content-text" text-anchor="middle" fill="white" font-size="18">场景</text>
  <text x="230" y="270" class="section-title" fill="#DC2626">场景回顾：</text>
  <text x="230" y="320" class="scenario-text">高价值企业客户因服务问题要求拆机</text>
  <text x="230" y="360" class="content-text">月费2000元，使用3年，影响面广</text>
  <text x="230" y="400" class="content-text">涉及多条专线，员工上百人</text>
  
  <!-- 演练任务 -->
  <rect x="100" y="440" width="1720" height="240" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="530" r="20" fill="#059669"/>
  <text x="180" y="540" class="content-text" text-anchor="middle" fill="white" font-size="20">任务</text>
  <text x="230" y="510" class="section-title" fill="#059669">演练任务：</text>
  <g transform="translate(280, 550)">
    <circle cx="0" cy="0" r="8" fill="#059669"/>
    <text x="20" y="8" class="task-text">分析客户痛点，找出根本原因</text>
    
    <circle cx="0" cy="40" r="8" fill="#059669"/>
    <text x="20" y="48" class="task-text">制定综合挽留策略</text>
    
    <circle cx="0" cy="80" r="8" fill="#059669"/>
    <text x="20" y="88" class="task-text">模拟高层沟通场景</text>
    
    <circle cx="0" cy="120" r="8" fill="#059669"/>
    <text x="20" y="128" class="task-text">设计长期合作方案</text>
  </g>
  
  <!-- 引导思考 -->
  <rect x="100" y="700" width="1720" height="240" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="790" r="20" fill="#2563EB"/>
  <text x="180" y="800" class="content-text" text-anchor="middle" fill="white" font-size="18">引导</text>
  <text x="230" y="770" class="section-title" fill="#2563EB">引导思考：</text>
  <g transform="translate(280, 810)">
    <circle cx="0" cy="0" r="8" fill="#2563EB"/>
    <text x="20" y="8" class="guide-text">如何平衡成本与收益？</text>
    
    <circle cx="0" cy="40" r="8" fill="#2563EB"/>
    <text x="20" y="48" class="guide-text">如何体现对高价值用户的重视？</text>
    
    <circle cx="0" cy="80" r="8" fill="#2563EB"/>
    <text x="20" y="88" class="guide-text">如何避免类似问题再次发生？</text>
    
    <circle cx="0" cy="120" r="8" fill="#2563EB"/>
    <text x="20" y="128" class="content-text">考虑企业客户的特殊需求和决策流程</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <!-- 高价值客户标识 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#D97706" stroke-width="4"/>
    <circle cx="0" cy="0" r="50" fill="#D97706" opacity="0.2"/>
    
    <!-- 钻石图标 -->
    <path d="M 0 -30 L -20 -10 L -15 15 L 0 25 L 15 15 L 20 -10 Z" 
          fill="#D97706" opacity="0.8"/>
    
    <text x="0" y="110" class="content-text" text-anchor="middle" font-size="20" fill="#D97706">高价值客户</text>
    
    <!-- 价值指标 -->
    <g transform="translate(0, 140)">
      <rect x="-60" y="0" width="120" height="30" fill="#D97706" opacity="0.3" rx="15"/>
      <text x="0" y="20" class="content-text" text-anchor="middle" font-size="18" fill="#D97706">2000元/月</text>
      
      <rect x="-60" y="40" width="120" height="30" fill="#DC2626" opacity="0.3" rx="15"/>
      <text x="0" y="60" class="content-text" text-anchor="middle" font-size="18" fill="#DC2626">流失风险</text>
    </g>
  </g>
</svg>
