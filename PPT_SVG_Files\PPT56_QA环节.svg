<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 72px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; fill: #374151; text-anchor: middle; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #374151; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 200 Q 960 100 1820 200" class="accent-curve" opacity="0.4"/>
  <path d="M 100 880 Q 960 980 1820 880" class="accent-curve" opacity="0.4"/>
  
  <!-- 主标题 -->
  <text x="960" y="350" class="title-text">Q&amp;A 环节</text>
  
  <!-- 副标题 -->
  <text x="960" y="450" class="subtitle-text">学员提问与解答</text>
  
  <!-- 互动说明 -->
  <rect x="300" y="520" width="1320" height="200" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="30"/>
  <text x="960" y="580" class="content-text" text-anchor="middle">欢迎大家提出问题</text>
  <text x="960" y="630" class="content-text" text-anchor="middle">关于激活与活性管理的任何疑问</text>
  <text x="960" y="680" class="content-text" text-anchor="middle">我们一起讨论解决</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(400, 600)">
    <!-- 问号图标 -->
    <circle cx="0" cy="0" r="60" fill="#2563EB" opacity="0.2"/>
    <text x="0" y="20" class="title-text" text-anchor="middle" font-size="80" fill="#2563EB">?</text>
  </g>
  
  <g transform="translate(1520, 600)">
    <!-- 感叹号图标 -->
    <circle cx="0" cy="0" r="60" fill="#059669" opacity="0.2"/>
    <text x="0" y="20" class="title-text" text-anchor="middle" font-size="80" fill="#059669">!</text>
  </g>
  
  <!-- 底部装饰 -->
  <circle cx="960" cy="800" r="30" fill="#2563EB" opacity="0.3"/>
  <circle cx="900" cy="820" r="15" fill="#059669" opacity="0.5"/>
  <circle cx="1020" cy="820" r="15" fill="#D97706" opacity="0.5"/>
  <circle cx="860" cy="840" r="10" fill="#DC2626" opacity="0.4"/>
  <circle cx="1060" cy="840" r="10" fill="#8B5CF6" opacity="0.4"/>
</svg>
