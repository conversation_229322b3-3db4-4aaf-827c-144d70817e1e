<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .path-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2563EB; font-weight: bold; }
      .function-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #059669; }
      .value-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">工具实操：爱家平台WiFi远程诊断</text>
  
  <!-- 操作路径 -->
  <rect x="100" y="200" width="1720" height="120" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="240" r="15" fill="#2563EB"/>
  <text x="220" y="230" class="section-title" fill="#2563EB">操作路径：</text>
  <text x="220" y="270" class="path-text">用户详情 → 网络质量 → 远程诊断</text>
  <text x="220" y="300" class="content-text">一键获取全面网络诊断报告</text>
  
  <!-- 能看什么 -->
  <rect x="100" y="340" width="1720" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="410" r="15" fill="#059669"/>
  <text x="220" y="390" class="section-title" fill="#059669">能看什么：</text>
  <g transform="translate(270, 420)">
    <circle cx="0" cy="0" r="8" fill="#059669"/>
    <text x="20" y="8" class="function-text">历史速率变化曲线</text>
    
    <circle cx="0" cy="35" r="8" fill="#059669"/>
    <text x="20" y="43" class="function-text">网络时延和丢包率</text>
    
    <circle cx="0" cy="70" r="8" fill="#059669"/>
    <text x="20" y="78" class="function-text">WiFi信号强度分布</text>
    
    <circle cx="0" cy="105" r="8" fill="#059669"/>
    <text x="20" y="113" class="function-text">连接设备列表和状态</text>
  </g>
  
  <!-- 诊断报告界面展示 -->
  <rect x="100" y="560" width="800" height="400" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
  <text x="500" y="600" class="content-text" text-anchor="middle" font-size="28">诊断报告界面展示</text>
  
  <!-- 模拟诊断数据 -->
  <g transform="translate(150, 640)">
    <rect x="0" y="0" width="700" height="50" fill="#DCFCE7" rx="5"/>
    <text x="20" y="30" class="content-text" font-size="24">网络速率：下行 485Mbps ↑ 上行 47Mbps</text>
    
    <rect x="0" y="60" width="700" height="50" fill="#FEF3C7" rx="5"/>
    <text x="20" y="90" class="content-text" font-size="24">网络时延：平均 12ms，丢包率 0.1%</text>
    
    <rect x="0" y="120" width="700" height="50" fill="#DBEAFE" rx="5"/>
    <text x="20" y="150" class="content-text" font-size="24">WiFi信号：客厅 -45dBm，卧室 -68dBm</text>
    
    <rect x="0" y="180" width="700" height="50" fill="#E5E7EB" rx="5"/>
    <text x="20" y="210" class="content-text" font-size="24">连接设备：手机3台，电脑2台，智能设备5台</text>
    
    <rect x="0" y="240" width="700" height="50" fill="#FEE2E2" rx="5"/>
    <text x="20" y="270" class="content-text" font-size="24">异常提醒：卧室WiFi信号偏弱，建议优化</text>
  </g>
  
  <!-- 作用说明 -->
  <rect x="950" y="560" width="870" height="400" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="1020" cy="610" r="15" fill="#DC2626"/>
  <text x="1060" y="590" class="section-title" fill="#DC2626">核心作用：</text>
  
  <text x="1060" y="640" class="value-text">远程判断问题</text>
  <text x="1060" y="680" class="content-text">无需上门即可了解网络状况</text>
  
  <text x="1060" y="730" class="value-text">减少无效上门</text>
  <text x="1060" y="770" class="content-text">提前识别问题类型和严重程度</text>
  
  <text x="1060" y="820" class="value-text">提升服务效率</text>
  <text x="1060" y="860" class="content-text">精准定位问题，快速制定解决方案</text>
  
  <text x="1060" y="910" class="content-text">为客户沟通提供数据支撑</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1700, 300)">
    <!-- 远程诊断图标 -->
    <circle cx="0" cy="0" r="50" fill="none" stroke="#2563EB" stroke-width="3"/>
    <rect x="-20" y="-15" width="40" height="30" fill="#2563EB" opacity="0.3" rx="5"/>
    <circle cx="0" cy="0" r="8" fill="#2563EB"/>
    
    <!-- 信号波纹 -->
    <circle cx="0" cy="0" r="70" fill="none" stroke="#059669" stroke-width="2" opacity="0.5"/>
    <circle cx="0" cy="0" r="90" fill="none" stroke="#059669" stroke-width="2" opacity="0.3"/>
    
    <text x="0" y="120" class="content-text" text-anchor="middle" font-size="20">远程诊断</text>
  </g>
</svg>
