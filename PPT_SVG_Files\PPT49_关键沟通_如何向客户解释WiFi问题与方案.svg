<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #374151; }
      .method-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #6B7280; font-style: italic; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">关键沟通：如何向客户解释WiFi问题与方案？（话术）</text>
  
  <!-- 解释问题 -->
  <rect x="100" y="200" width="1720" height="280" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="280" r="20" fill="#059669"/>
  <text x="180" y="290" class="speech-text" text-anchor="middle" fill="white" font-size="18">解释</text>
  <text x="230" y="250" class="section-title" fill="#059669">解释问题：</text>
  
  <text x="230" y="300" class="speech-text">"根据热力图（展示），您看书房这块信号确实比较弱，</text>
  <text x="230" y="340" class="speech-text">主要是离得远加上隔了堵承重墙。"</text>
  
  <text x="230" y="400" class="method-text">（可视化展示 + 说明原因）</text>
  <text x="230" y="440" class="highlight-text">关键：用数据说话，让客户看得见问题</text>
  
  <!-- 推荐方案 -->
  <rect x="100" y="500" width="1720" height="320" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="600" r="20" fill="#2563EB"/>
  <text x="180" y="610" class="speech-text" text-anchor="middle" fill="white" font-size="18">方案</text>
  <text x="230" y="570" class="section-title" fill="#2563EB">推荐方案（Mesh）：</text>
  
  <text x="230" y="620" class="speech-text">"想彻底解决全屋覆盖，最好的办法是用Mesh组网。</text>
  <text x="230" y="660" class="speech-text">简单说就是在信号不好的地方加个小路由，</text>
  <text x="230" y="700" class="speech-text">它们能智能连接，走到哪信号都好。</text>
  <text x="230" y="740" class="speech-text">现在办理有优惠..."</text>
  
  <text x="230" y="780" class="method-text">（说方案 + 讲好处 + 促销售）</text>
  <text x="230" y="820" class="highlight-text">关键：简单易懂 + 突出价值 + 优惠刺激</text>
  
  <!-- 沟通要点 -->
  <rect x="100" y="840" width="1720" height="160" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="900" r="20" fill="#D97706"/>
  <text x="180" y="910" class="speech-text" text-anchor="middle" fill="white" font-size="18">要点</text>
  <text x="230" y="880" class="section-title" fill="#D97706">沟通要点总结：</text>
  
  <g transform="translate(280, 920)">
    <circle cx="0" cy="0" r="8" fill="#D97706"/>
    <text x="20" y="8" class="speech-text">用热力图等可视化工具展示问题</text>
    
    <circle cx="400" cy="0" r="8" fill="#D97706"/>
    <text x="420" y="8" class="speech-text">用通俗语言解释技术原因</text>
    
    <circle cx="800" cy="0" r="8" fill="#D97706"/>
    <text x="820" y="8" class="speech-text">重点推荐Mesh解决方案</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 450)">
    <!-- 沟通示意图 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#059669" stroke-width="3" opacity="0.3"/>
    
    <!-- 热力图图标 -->
    <rect x="-30" y="-20" width="60" height="40" fill="#059669" opacity="0.2" rx="5"/>
    <rect x="-20" y="-10" width="15" height="20" fill="#059669"/>
    <rect x="-5" y="-10" width="15" height="20" fill="#D97706"/>
    <rect x="10" y="-10" width="15" height="20" fill="#DC2626"/>
    
    <text x="0" y="110" class="speech-text" text-anchor="middle" font-size="20">可视化沟通</text>
    
    <!-- Mesh图标 -->
    <g transform="translate(0, 150)">
      <circle cx="-20" cy="0" r="8" fill="#2563EB"/>
      <circle cx="20" cy="0" r="8" fill="#2563EB"/>
      <circle cx="0" cy="20" r="8" fill="#2563EB"/>
      <line x1="-20" y1="0" x2="20" y2="0" stroke="#2563EB" stroke-width="2"/>
      <line x1="-20" y1="0" x2="0" y2="20" stroke="#2563EB" stroke-width="2"/>
      <line x1="20" y1="0" x2="0" y2="20" stroke="#2563EB" stroke-width="2"/>
      <text x="0" y="45" class="speech-text" text-anchor="middle" font-size="18">Mesh组网</text>
    </g>
  </g>
</svg>
