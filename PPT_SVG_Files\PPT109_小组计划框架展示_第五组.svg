<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .group-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #2563EB; font-weight: bold; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #374151; }
      .framework-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #059669; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="200" class="title-text">小组计划框架展示</text>
  <text x="960" y="280" class="group-title">第五组</text>
  
  <!-- 计划框架示例 -->
  <rect x="150" y="320" width="1620" height="500" fill="#F9FAFB" stroke="#6B7280" stroke-width="3" rx="30"/>
  <circle cx="220" cy="520" r="20" fill="#6B7280"/>
  <text x="220" y="530" class="content-text" text-anchor="middle" fill="white" font-size="24">框架</text>
  <text x="270" y="380" class="section-title" fill="#6B7280">计划框架供参考：</text>
  
  <!-- 框架内容 -->
  <g transform="translate(320, 420)">
    <!-- 区域团队 -->
    <rect x="0" y="0" width="1200" height="60" fill="#EFF6FF" rx="10"/>
    <text x="20" y="25" class="framework-text" fill="#2563EB">区域/团队：</text>
    <text x="20" y="50" class="content-text">南充顺庆区综合运营团队</text>
    
    <!-- 核心问题 -->
    <rect x="0" y="80" width="1200" height="80" fill="#FEF2F2" rx="10"/>
    <text x="20" y="105" class="framework-text" fill="#DC2626">核心问题诊断：</text>
    <text x="20" y="130" class="content-text">1. 用户活跃度管理缺乏系统性方法</text>
    <text x="20" y="150" class="content-text">2. 异常处置响应速度有待提升</text>
    
    <!-- 改进目标 -->
    <rect x="0" y="180" width="1200" height="60" fill="#F0FDF4" rx="10"/>
    <text x="20" y="205" class="framework-text" fill="#059669">改进总目标（90天）：</text>
    <text x="20" y="230" class="content-text">用户活跃度提升15%，异常处置时长缩短40%</text>
    
    <!-- 关键动作 -->
    <rect x="0" y="260" width="1200" height="100" fill="#FEF3C7" rx="10"/>
    <text x="20" y="285" class="framework-text" fill="#D97706">关键动作1：</text>
    <text x="20" y="310" class="content-text">建立活跃度分层管理体系，优化异常预警机制</text>
    <text x="20" y="335" class="content-text">责任人：运营主管  时间：45天内完成</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 550)">
    <!-- 第五组标识 -->
    <circle cx="0" cy="0" r="80" fill="#2563EB" opacity="0.6"/>
    <text x="0" y="-5" class="content-text" text-anchor="middle" fill="white" font-size="32">第五组</text>
    <text x="0" y="20" class="content-text" text-anchor="middle" fill="white" font-size="20">示例</text>
    
    <!-- 完成度指示 -->
    <g transform="translate(0, 120)">
      <rect x="-60" y="0" width="120" height="20" fill="#E5E7EB" rx="10"/>
      <rect x="-60" y="0" width="95" height="20" fill="#2563EB" rx="10"/>
      <text x="0" y="35" class="content-text" text-anchor="middle" font-size="16">框架完成</text>
    </g>
  </g>
</svg>
