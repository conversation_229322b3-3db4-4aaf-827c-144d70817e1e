<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .role-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: bold; }
      .action-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #374151; }
      .core-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">预警来了怎么办？（一线协同）</text>
  
  <!-- 收到预警 -->
  <rect x="100" y="220" width="1720" height="140" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="270" r="20" fill="#DC2626"/>
  <text x="180" y="280" class="content-text" text-anchor="middle" fill="white" font-size="18">预警</text>
  <text x="230" y="260" class="section-title" fill="#DC2626">收到预警工单/提醒：</text>
  <text x="230" y="300" class="content-text">意味着这个客户需要重点关注</text>
  <text x="230" y="340" class="content-text">系统已识别出潜在流失风险</text>
  
  <!-- 客户经理/网格经理 -->
  <rect x="100" y="380" width="1720" height="200" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="450" r="20" fill="#2563EB"/>
  <text x="180" y="460" class="content-text" text-anchor="middle" fill="white" font-size="16">经理</text>
  <text x="230" y="430" class="section-title" fill="#2563EB">客户经理/网格经理：</text>
  <g transform="translate(280, 460)">
    <circle cx="0" cy="0" r="8" fill="#2563EB"/>
    <text x="20" y="8" class="action-text">主动联系客户，了解情况</text>
    
    <circle cx="0" cy="40" r="8" fill="#2563EB"/>
    <text x="20" y="48" class="action-text">安抚情绪，表达关怀</text>
    
    <circle cx="0" cy="80" r="8" fill="#2563EB"/>
    <text x="20" y="88" class="action-text">协调解决问题，制定挽留方案</text>
  </g>
  
  <!-- 装维/客服 -->
  <rect x="100" y="600" width="1720" height="180" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="660" r="20" fill="#059669"/>
  <text x="180" y="670" class="content-text" text-anchor="middle" fill="white" font-size="16">服务</text>
  <text x="230" y="640" class="section-title" fill="#059669">装维/客服：</text>
  <g transform="translate(280, 670)">
    <circle cx="0" cy="0" r="8" fill="#059669"/>
    <text x="20" y="8" class="action-text">优先处理预警用户的服务请求</text>
    
    <circle cx="0" cy="40" r="8" fill="#059669"/>
    <text x="20" y="48" class="action-text">提升服务标准，确保一次解决</text>
    
    <circle cx="0" cy="80" r="8" fill="#059669"/>
    <text x="20" y="88" class="action-text">及时反馈处理结果</text>
  </g>
  
  <!-- 核心理念 -->
  <rect x="100" y="800" width="1720" height="160" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="860" r="20" fill="#D97706"/>
  <text x="180" y="870" class="content-text" text-anchor="middle" fill="white" font-size="18">核心</text>
  <text x="230" y="840" class="section-title" fill="#D97706">核心理念：</text>
  <text x="230" y="890" class="core-text">主动沟通，快速响应，协同作战</text>
  <text x="230" y="930" class="content-text">让客户感受到我们的重视和专业</text>
  
  <!-- 装饰元素 - 协同示意图 -->
  <g transform="translate(1600, 500)">
    <!-- 协同圆环 -->
    <circle cx="0" cy="0" r="100" fill="none" stroke="#3B82F6" stroke-width="3" opacity="0.3"/>
    
    <!-- 角色节点 -->
    <circle cx="0" cy="-80" r="30" fill="#DC2626" opacity="0.6"/>
    <text x="0" y="-70" class="content-text" text-anchor="middle" fill="white" font-size="16">预警</text>
    
    <circle cx="70" cy="40" r="30" fill="#2563EB" opacity="0.6"/>
    <text x="70" y="50" class="content-text" text-anchor="middle" fill="white" font-size="16">经理</text>
    
    <circle cx="-70" cy="40" r="30" fill="#059669" opacity="0.6"/>
    <text x="-70" y="50" class="content-text" text-anchor="middle" fill="white" font-size="16">服务</text>
    
    <!-- 连接线 -->
    <line x1="0" y1="-50" x2="50" y2="20" stroke="#6B7280" stroke-width="2"/>
    <line x1="0" y1="-50" x2="-50" y2="20" stroke="#6B7280" stroke-width="2"/>
    <line x1="40" y1="40" x2="-40" y2="40" stroke="#6B7280" stroke-width="2"/>
    
    <text x="0" y="140" class="content-text" text-anchor="middle" font-size="20">协同作战</text>
  </g>
</svg>
