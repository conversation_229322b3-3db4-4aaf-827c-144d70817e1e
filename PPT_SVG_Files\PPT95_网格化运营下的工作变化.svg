<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .change-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .desc-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #6B7280; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="200" class="title-text">网格化运营下的工作变化</text>
  
  <!-- 更精准 -->
  <rect x="150" y="260" width="1620" height="180" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="30"/>
  <circle cx="220" cy="320" r="20" fill="#2563EB"/>
  <text x="220" y="330" class="content-text" text-anchor="middle" fill="white" font-size="24">精准</text>
  <text x="270" y="310" class="change-text" fill="#2563EB">更精准：</text>
  <text x="270" y="360" class="content-text">任务推送更精准，服务对象更明确</text>
  <text x="270" y="400" class="desc-text">基于网格数据，实现精细化运营</text>
  
  <!-- 更协同 -->
  <rect x="150" y="460" width="1620" height="180" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="30"/>
  <circle cx="220" cy="520" r="20" fill="#059669"/>
  <text x="220" y="530" class="content-text" text-anchor="middle" fill="white" font-size="24">协同</text>
  <text x="270" y="510" class="change-text" fill="#059669">更协同：</text>
  <text x="270" y="560" class="content-text">需要与网格经理及其他条线更紧密配合</text>
  <text x="270" y="600" class="desc-text">打破部门壁垒，形成合力</text>
  
  <!-- 更闭环 -->
  <rect x="150" y="660" width="1620" height="180" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="30"/>
  <circle cx="220" cy="720" r="20" fill="#D97706"/>
  <text x="220" y="730" class="content-text" text-anchor="middle" fill="white" font-size="24">闭环</text>
  <text x="270" y="710" class="change-text" fill="#D97706">更闭环：</text>
  <text x="270" y="760" class="content-text">服务和营销效果直接反馈到网格，责任更清晰</text>
  <text x="270" y="800" class="desc-text">全流程可追溯，结果可衡量</text>
  
  <!-- 装饰元素 - 变化对比图 -->
  <g transform="translate(1500, 550)">
    <!-- 变化箭头 -->
    <rect x="0" y="0" width="300" height="200" fill="#F9FAFB" stroke="#D1D5DB" stroke-width="2" rx="15"/>
    <text x="150" y="30" class="content-text" text-anchor="middle" font-size="24">工作变化</text>
    
    <!-- 传统模式 -->
    <g transform="translate(30, 60)">
      <rect x="0" y="0" width="80" height="40" fill="#E5E7EB" rx="5"/>
      <text x="40" y="25" class="content-text" text-anchor="middle" font-size="16">传统</text>
      
      <text x="40" y="55" class="content-text" text-anchor="middle" font-size="12">粗放</text>
      <text x="40" y="70" class="content-text" text-anchor="middle" font-size="12">分散</text>
      <text x="40" y="85" class="content-text" text-anchor="middle" font-size="12">开环</text>
    </g>
    
    <!-- 箭头 -->
    <path d="M 120 80 L 180 80" stroke="#2563EB" stroke-width="4" marker-end="url(#arrowhead)"/>
    
    <!-- 网格模式 -->
    <g transform="translate(190, 60)">
      <rect x="0" y="0" width="80" height="40" fill="#2563EB" opacity="0.6" rx="5"/>
      <text x="40" y="25" class="content-text" text-anchor="middle" fill="white" font-size="16">网格</text>
      
      <text x="40" y="55" class="content-text" text-anchor="middle" font-size="12" fill="#2563EB">精准</text>
      <text x="40" y="70" class="content-text" text-anchor="middle" font-size="12" fill="#059669">协同</text>
      <text x="40" y="85" class="content-text" text-anchor="middle" font-size="12" fill="#D97706">闭环</text>
    </g>
    
    <text x="150" y="180" class="content-text" text-anchor="middle" font-size="20">运营升级</text>
  </g>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2563EB"/>
    </marker>
  </defs>
</svg>
