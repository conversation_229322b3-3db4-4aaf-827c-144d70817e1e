<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .phase-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #DC2626; font-weight: bold; text-anchor: middle; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #059669; font-style: italic; }
      .tip-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #8B5CF6; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">第一阶：共情接纳（稳定情绪）</text>
  
  <!-- Step 1: 倾听与复述 -->
  <rect x="100" y="220" width="1720" height="240" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="310" r="25" fill="#DC2626"/>
  <text x="180" y="320" class="content-text" text-anchor="middle" fill="white" font-size="24">1</text>
  <text x="230" y="280" class="step-title" fill="#DC2626">Step 1: 倾听与复述</text>
  <text x="230" y="320" class="content-text">确认客户诉求，让客户感受到被理解</text>
  
  <rect x="250" y="350" width="1400" height="80" fill="#FECACA" rx="10"/>
  <text x="270" y="380" class="example-text">"我听明白了，您是说网络经常断线，</text>
  <text x="270" y="410" class="example-text">影响了您在家办公，让您很困扰，是这样吗？"</text>
  
  <!-- Step 2: 共情与道歉 -->
  <rect x="100" y="480" width="1720" height="240" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="570" r="25" fill="#DC2626"/>
  <text x="180" y="580" class="content-text" text-anchor="middle" fill="white" font-size="24">2</text>
  <text x="230" y="540" class="step-title" fill="#DC2626">Step 2: 共情与道歉</text>
  <text x="230" y="580" class="content-text">表达理解和歉意，建立情感连接</text>
  
  <rect x="250" y="610" width="1400" height="80" fill="#FECACA" rx="10"/>
  <text x="270" y="640" class="example-text">"非常理解您的感受，网络不稳定确实很影响工作。</text>
  <text x="270" y="670" class="example-text">给您添麻烦了，非常抱歉！我们一定帮您解决。"</text>
  
  <!-- 技巧提示 -->
  <rect x="100" y="740" width="1720" height="180" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="810" r="20" fill="#8B5CF6"/>
  <text x="180" y="820" class="content-text" text-anchor="middle" fill="white" font-size="18">技巧</text>
  <text x="230" y="790" class="step-title" fill="#8B5CF6">沟通技巧：</text>
  <g transform="translate(280, 820)">
    <circle cx="0" cy="0" r="8" fill="#8B5CF6"/>
    <text x="20" y="8" class="tip-text">语气真诚，避免机械化回复</text>
    
    <circle cx="0" cy="40" r="8" fill="#8B5CF6"/>
    <text x="20" y="48" class="tip-text">适当降调，表达歉意和关怀</text>
    
    <circle cx="0" cy="80" r="8" fill="#8B5CF6"/>
    <text x="20" y="88" class="tip-text">重复关键信息，确认理解准确</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <!-- 共情图标 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#DC2626" stroke-width="3" opacity="0.3"/>
    <circle cx="0" cy="0" r="50" fill="#DC2626" opacity="0.1"/>
    
    <!-- 心形图标 -->
    <path d="M 0 15 C -15 0 -30 0 -30 -15 C -30 -30 -15 -30 0 -15 C 15 -30 30 -30 30 -15 C 30 0 15 0 0 15 Z" 
          fill="#DC2626" opacity="0.6"/>
    
    <text x="0" y="120" class="content-text" text-anchor="middle" font-size="20" fill="#DC2626">共情接纳</text>
    
    <!-- 情绪稳定指示器 -->
    <g transform="translate(0, 160)">
      <rect x="-40" y="0" width="80" height="20" fill="#E5E7EB" rx="10"/>
      <rect x="-40" y="0" width="60" height="20" fill="#DC2626" rx="10"/>
      <text x="0" y="35" class="content-text" text-anchor="middle" font-size="16">情绪稳定</text>
    </g>
  </g>
</svg>
