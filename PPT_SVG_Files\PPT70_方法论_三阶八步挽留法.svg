<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .phase-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #374151; }
      .step-number { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">方法论：三阶八步挽留法</text>
  
  <!-- 三个阶段展示 -->
  <g transform="translate(960, 550)">
    <!-- 第一阶：共情接纳 -->
    <g transform="translate(-500, 0)">
      <rect x="-150" y="-200" width="300" height="400" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
      <circle cx="0" cy="-150" r="30" fill="#DC2626"/>
      <text x="0" y="-140" class="phase-title" text-anchor="middle" fill="white" font-size="24">第一阶</text>
      
      <text x="0" y="-100" class="phase-title" text-anchor="middle" fill="#DC2626">共情接纳</text>
      <text x="0" y="-70" class="step-text" text-anchor="middle" fill="#DC2626">（稳定情绪）</text>
      
      <!-- 步骤1-2 -->
      <g transform="translate(0, -30)">
        <circle cx="-80" cy="0" r="20" fill="#DC2626" opacity="0.8"/>
        <text x="-80" y="5" class="step-number" text-anchor="middle" fill="white">1</text>
        <text x="-80" y="35" class="step-text" text-anchor="middle" font-size="18">倾听</text>
        <text x="-80" y="55" class="step-text" text-anchor="middle" font-size="18">复述</text>
        
        <circle cx="80" cy="0" r="20" fill="#DC2626" opacity="0.8"/>
        <text x="80" y="5" class="step-number" text-anchor="middle" fill="white">2</text>
        <text x="80" y="35" class="step-text" text-anchor="middle" font-size="18">共情</text>
        <text x="80" y="55" class="step-text" text-anchor="middle" font-size="18">道歉</text>
      </g>
    </g>
    
    <!-- 第二阶：方案呈现 -->
    <g transform="translate(0, 0)">
      <rect x="-150" y="-200" width="300" height="400" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
      <circle cx="0" cy="-150" r="30" fill="#2563EB"/>
      <text x="0" y="-140" class="phase-title" text-anchor="middle" fill="white" font-size="24">第二阶</text>
      
      <text x="0" y="-100" class="phase-title" text-anchor="middle" fill="#2563EB">方案呈现</text>
      <text x="0" y="-70" class="step-text" text-anchor="middle" fill="#2563EB">（解决问题）</text>
      
      <!-- 步骤3-6 -->
      <g transform="translate(0, -30)">
        <circle cx="-80" cy="-40" r="18" fill="#2563EB" opacity="0.8"/>
        <text x="-80" y="-35" class="step-number" text-anchor="middle" fill="white" font-size="16">3</text>
        <text x="-80" y="-15" class="step-text" text-anchor="middle" font-size="16">分析诊断</text>
        
        <circle cx="80" cy="-40" r="18" fill="#2563EB" opacity="0.8"/>
        <text x="80" y="-35" class="step-number" text-anchor="middle" fill="white" font-size="16">4</text>
        <text x="80" y="-15" class="step-text" text-anchor="middle" font-size="16">方案制定</text>
        
        <circle cx="-80" cy="40" r="18" fill="#2563EB" opacity="0.8"/>
        <text x="-80" y="45" class="step-number" text-anchor="middle" fill="white" font-size="16">5</text>
        <text x="-80" y="65" class="step-text" text-anchor="middle" font-size="16">价值重塑</text>
        
        <circle cx="80" cy="40" r="18" fill="#2563EB" opacity="0.8"/>
        <text x="80" y="45" class="step-number" text-anchor="middle" fill="white" font-size="16">6</text>
        <text x="80" y="65" class="step-text" text-anchor="middle" font-size="16">引导选择</text>
      </g>
    </g>
    
    <!-- 第三阶：闭环确认 -->
    <g transform="translate(500, 0)">
      <rect x="-150" y="-200" width="300" height="400" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
      <circle cx="0" cy="-150" r="30" fill="#059669"/>
      <text x="0" y="-140" class="phase-title" text-anchor="middle" fill="white" font-size="24">第三阶</text>
      
      <text x="0" y="-100" class="phase-title" text-anchor="middle" fill="#059669">闭环确认</text>
      <text x="0" y="-70" class="step-text" text-anchor="middle" fill="#059669">（防止再发）</text>
      
      <!-- 步骤7-8 -->
      <g transform="translate(0, -30)">
        <circle cx="-80" cy="0" r="20" fill="#059669" opacity="0.8"/>
        <text x="-80" y="5" class="step-number" text-anchor="middle" fill="white">7</text>
        <text x="-80" y="35" class="step-text" text-anchor="middle" font-size="18">执行</text>
        <text x="-80" y="55" class="step-text" text-anchor="middle" font-size="18">跟踪</text>
        
        <circle cx="80" cy="0" r="20" fill="#059669" opacity="0.8"/>
        <text x="80" y="5" class="step-number" text-anchor="middle" fill="white">8</text>
        <text x="80" y="35" class="step-text" text-anchor="middle" font-size="18">回访</text>
        <text x="80" y="55" class="step-text" text-anchor="middle" font-size="18">验证</text>
      </g>
    </g>
    
    <!-- 连接箭头 -->
    <path d="M -300 0 L -200 0" stroke="#6B7280" stroke-width="4" marker-end="url(#arrowhead)"/>
    <path d="M 200 0 L 300 0" stroke="#6B7280" stroke-width="4" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- 底部说明 -->
  <rect x="200" y="850" width="1520" height="120" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <text x="960" y="890" class="phase-title" text-anchor="middle" fill="#D97706">系统化挽留方法</text>
  <text x="960" y="920" class="step-text" text-anchor="middle">情理并重，步步为营</text>
  <text x="960" y="950" class="step-text" text-anchor="middle">确保挽留效果最大化</text>
  
  <defs>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#6B7280"/>
    </marker>
  </defs>
</svg>
