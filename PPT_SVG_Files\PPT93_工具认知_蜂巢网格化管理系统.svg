<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #374151; }
      .function-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; }
      .desc-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #6B7280; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">工具认知："蜂巢"网格化管理系统</text>
  
  <!-- 核心功能 -->
  <rect x="100" y="200" width="1720" height="680" fill="#F9FAFB" stroke="#6B7280" stroke-width="3" rx="20"/>
  <circle cx="180" cy="450" r="20" fill="#6B7280"/>
  <text x="180" y="460" class="content-text" text-anchor="middle" fill="white" font-size="18">功能</text>
  <text x="230" y="250" class="section-title" fill="#6B7280">核心功能（图文/截图展示）：</text>
  
  <!-- 功能1：GIS作战地图 -->
  <g transform="translate(280, 290)">
    <rect x="0" y="0" width="1300" height="80" fill="#EFF6FF" stroke="#2563EB" stroke-width="2" rx="10"/>
    <circle cx="20" cy="20" r="15" fill="#2563EB"/>
    <text x="20" y="28" class="content-text" text-anchor="middle" fill="white" font-size="16">🗺️</text>
    <text x="50" y="30" class="function-text" fill="#2563EB">GIS作战地图：</text>
    <text x="50" y="60" class="desc-text">用户/资源/工单/告警可视化展示</text>
  </g>
  
  <!-- 功能2：网格健康度仪表盘 -->
  <g transform="translate(280, 390)">
    <rect x="0" y="0" width="1300" height="80" fill="#F0FDF4" stroke="#059669" stroke-width="2" rx="10"/>
    <circle cx="20" cy="20" r="15" fill="#059669"/>
    <text x="20" y="28" class="content-text" text-anchor="middle" fill="white" font-size="16">📊</text>
    <text x="50" y="30" class="function-text" fill="#059669">网格健康度仪表盘：</text>
    <text x="50" y="60" class="desc-text">红黄绿预警，实时监控网格状态</text>
  </g>
  
  <!-- 功能3：网格任务中心 -->
  <g transform="translate(280, 490)">
    <rect x="0" y="0" width="1300" height="80" fill="#FEF3C7" stroke="#D97706" stroke-width="2" rx="10"/>
    <circle cx="20" cy="20" r="15" fill="#D97706"/>
    <text x="20" y="28" class="content-text" text-anchor="middle" fill="white" font-size="16">📋</text>
    <text x="50" y="30" class="function-text" fill="#D97706">网格任务中心：</text>
    <text x="50" y="60" class="desc-text">接收/处理/反馈任务闭环管理</text>
  </g>
  
  <!-- 功能4：用户与资源管理 -->
  <g transform="translate(280, 590)">
    <rect x="0" y="0" width="1300" height="80" fill="#FEE2E2" stroke="#DC2626" stroke-width="2" rx="10"/>
    <circle cx="20" cy="20" r="15" fill="#DC2626"/>
    <text x="20" y="28" class="content-text" text-anchor="middle" fill="white" font-size="16">👥</text>
    <text x="50" y="30" class="function-text" fill="#DC2626">用户与资源管理：</text>
    <text x="50" y="60" class="desc-text">网格内用户档案和资源配置统一管理</text>
  </g>
  
  <!-- 功能5：在线协同与报表 -->
  <g transform="translate(280, 690)">
    <rect x="0" y="0" width="1300" height="80" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="2" rx="10"/>
    <circle cx="20" cy="20" r="15" fill="#8B5CF6"/>
    <text x="20" y="28" class="content-text" text-anchor="middle" fill="white" font-size="16">🤝</text>
    <text x="50" y="30" class="function-text" fill="#8B5CF6">在线协同与报表：</text>
    <text x="50" y="60" class="desc-text">团队协作沟通和数据分析报表</text>
  </g>
  
  <!-- 装饰元素 - 蜂巢系统架构图 -->
  <g transform="translate(1500, 600)">
    <!-- 蜂巢结构 -->
    <g transform="translate(0, 0)">
      <!-- 六边形蜂巢 -->
      <polygon points="0,-40 35,-20 35,20 0,40 -35,20 -35,-20" 
               fill="#D97706" opacity="0.6" stroke="#D97706" stroke-width="2"/>
      <text x="0" y="5" class="content-text" text-anchor="middle" fill="white" font-size="14">蜂巢</text>
      
      <!-- 周围功能模块 -->
      <polygon points="70,-40 105,-20 105,20 70,40 35,20 35,-20" 
               fill="#2563EB" opacity="0.4" stroke="#2563EB" stroke-width="1"/>
      <text x="70" y="5" class="content-text" text-anchor="middle" fill="white" font-size="12">地图</text>
      
      <polygon points="-70,-40 -35,-20 -35,20 -70,40 -105,20 -105,-20" 
               fill="#059669" opacity="0.4" stroke="#059669" stroke-width="1"/>
      <text x="-70" y="5" class="content-text" text-anchor="middle" fill="white" font-size="12">监控</text>
      
      <polygon points="35,-80 70,-60 70,-20 35,0 0,-20 0,-60" 
               fill="#8B5CF6" opacity="0.4" stroke="#8B5CF6" stroke-width="1"/>
      <text x="35" y="-35" class="content-text" text-anchor="middle" fill="white" font-size="12">协同</text>
      
      <polygon points="-35,-80 0,-60 0,-20 -35,0 -70,-20 -70,-60" 
               fill="#DC2626" opacity="0.4" stroke="#DC2626" stroke-width="1"/>
      <text x="-35" y="-35" class="content-text" text-anchor="middle" fill="white" font-size="12">管理</text>
      
      <polygon points="35,80 70,60 70,20 35,0 0,20 0,60" 
               fill="#D97706" opacity="0.4" stroke="#D97706" stroke-width="1"/>
      <text x="35" y="45" class="content-text" text-anchor="middle" fill="white" font-size="12">任务</text>
    </g>
    
    <text x="0" y="120" class="content-text" text-anchor="middle" font-size="20">蜂巢架构</text>
  </g>
</svg>
