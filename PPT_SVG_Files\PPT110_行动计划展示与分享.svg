<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #8B5CF6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 4; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 150 Q 960 50 1820 150" class="accent-curve" opacity="0.4"/>
  <path d="M 100 930 Q 960 1030 1820 930" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="220" class="title-text">行动计划展示与分享</text>
  <text x="960" y="290" class="subtitle-text">（选取1-2组）</text>
  
  <!-- 展示环节 -->
  <rect x="150" y="340" width="1620" height="200" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="30"/>
  <circle cx="220" cy="410" r="20" fill="#059669"/>
  <text x="220" y="420" class="content-text" text-anchor="middle" fill="white" font-size="24">展示</text>
  <text x="270" y="390" class="section-title" fill="#059669">展示环节：</text>
  <text x="270" y="440" class="content-text">小组代表上台简要介绍计划</text>
  <text x="270" y="480" class="content-text">重点阐述问题诊断和解决思路</text>
  <text x="270" y="520" class="content-text">分享创新做法和实施要点</text>
  
  <!-- 互动点评 -->
  <rect x="150" y="560" width="1620" height="240" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="30"/>
  <circle cx="220" cy="650" r="20" fill="#2563EB"/>
  <text x="220" y="660" class="content-text" text-anchor="middle" fill="white" font-size="24">点评</text>
  <text x="270" y="620" class="section-title" fill="#2563EB">互动点评：</text>
  <text x="270" y="670" class="content-text">讲师/其他学员进行提问或点评</text>
  <text x="270" y="710" class="content-text">针对计划的可行性和创新性进行讨论</text>
  <text x="270" y="750" class="content-text">提供改进建议和经验分享</text>
  <text x="270" y="790" class="content-text">促进相互学习和启发</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1500, 550)">
    <!-- 展示分享图标 -->
    <circle cx="0" cy="0" r="100" fill="none" stroke="#DC2626" stroke-width="4"/>
    <circle cx="0" cy="0" r="70" fill="#DC2626" opacity="0.1"/>
    
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="28" fill="#DC2626">展示</text>
    <text x="0" y="25" class="content-text" text-anchor="middle" font-size="28" fill="#DC2626">分享</text>
    
    <!-- 互动元素 -->
    <circle cx="-60" cy="-60" r="20" fill="#059669" opacity="0.6"/>
    <text x="-60" y="-50" class="content-text" text-anchor="middle" fill="white" font-size="16">汇报</text>
    
    <circle cx="60" cy="-60" r="20" fill="#2563EB" opacity="0.6"/>
    <text x="60" y="-50" class="content-text" text-anchor="middle" fill="white" font-size="16">提问</text>
    
    <circle cx="-60" cy="60" r="20" fill="#D97706" opacity="0.6"/>
    <text x="-60" y="70" class="content-text" text-anchor="middle" fill="white" font-size="16">点评</text>
    
    <circle cx="60" cy="60" r="20" fill="#8B5CF6" opacity="0.6"/>
    <text x="60" y="70" class="content-text" text-anchor="middle" fill="white" font-size="16">建议</text>
    
    <text x="0" y="140" class="content-text" text-anchor="middle" font-size="20">互动交流</text>
  </g>
  
  <!-- 底部强调 -->
  <rect x="200" y="820" width="1520" height="100" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="25"/>
  <text x="960" y="860" class="highlight-text" text-anchor="middle" fill="#D97706">重点关注计划的可操作性和创新性</text>
  <text x="960" y="900" class="content-text" text-anchor="middle">通过分享交流，相互启发，共同提升</text>
</svg>
