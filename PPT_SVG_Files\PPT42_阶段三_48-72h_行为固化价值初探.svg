<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .phase-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #2563EB; font-weight: bold; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .tool-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #8B5CF6; font-weight: bold; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2563EB; font-style: italic; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">阶段三（48-72h）：行为固化 &amp; 价值初探</text>
  
  <!-- 动作 -->
  <rect x="100" y="220" width="1720" height="220" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="300" r="20" fill="#2563EB"/>
  <text x="180" y="310" class="content-text" text-anchor="middle" fill="white" font-size="20">动作</text>
  <text x="230" y="270" class="section-title" fill="#2563EB">关键动作：</text>
  
  <g transform="translate(280, 310)">
    <circle cx="0" cy="0" r="12" fill="#2563EB"/>
    <text x="30" y="8" class="content-text">引导参与积分/任务活动</text>
    
    <circle cx="0" cy="50" r="12" fill="#2563EB"/>
    <text x="30" y="58" class="content-text">推送首单增值服务特惠</text>
    
    <circle cx="0" cy="100" r="12" fill="#2563EB"/>
    <text x="30" y="108" class="content-text">邀请NPS评分/推荐有礼</text>
    
    <circle cx="0" cy="150" r="12" fill="#2563EB"/>
    <text x="30" y="158" class="content-text">建立长期互动习惯</text>
  </g>
  
  <!-- 工具 -->
  <rect x="100" y="460" width="1720" height="140" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="510" r="20" fill="#8B5CF6"/>
  <text x="180" y="520" class="content-text" text-anchor="middle" fill="white" font-size="20">工具</text>
  <text x="230" y="490" class="section-title" fill="#8B5CF6">核心工具：</text>
  <text x="230" y="530" class="tool-text">积分系统 + 营销引擎 + NPS工具</text>
  <text x="230" y="570" class="content-text">多维度激励，建立用户粘性</text>
  
  <!-- 消息模板示例 -->
  <rect x="100" y="620" width="1720" height="240" fill="#DBEAFE" stroke="#3B82F6" stroke-width="3" rx="20"/>
  <circle cx="180" cy="710" r="20" fill="#3B82F6"/>
  <text x="180" y="720" class="content-text" text-anchor="middle" fill="white" font-size="18">示例</text>
  <text x="230" y="690" class="section-title" fill="#3B82F6">消息模板示例：</text>
  
  <g transform="translate(280, 730)">
    <rect x="0" y="0" width="1200" height="40" fill="#DBEAFE" rx="5"/>
    <text x="20" y="25" class="example-text">新手任务："完成签到任务，赢取100积分，可兑换话费券..."</text>
    
    <rect x="0" y="50" width="1200" height="40" fill="#DBEAFE" rx="5"/>
    <text x="20" y="75" class="example-text">首单优惠："新用户专享，智能家居套餐首月1元，立即体验..."</text>
    
    <rect x="0" y="100" width="1200" height="40" fill="#DBEAFE" rx="5"/>
    <text x="20" y="125" class="example-text">推荐有礼："邀请好友办宽带，您和好友各得50元话费奖励..."</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 400)">
    <!-- 48-72小时时钟 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#2563EB" stroke-width="3"/>
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="24" fill="#2563EB">48-72h</text>
    <text x="0" y="15" class="content-text" text-anchor="middle" font-size="20" fill="#2563EB">第三阶段</text>
    
    <!-- 时钟指针 -->
    <line x1="0" y1="0" x2="0" y2="-50" stroke="#2563EB" stroke-width="3"/>
    <line x1="0" y1="0" x2="35" y2="0" stroke="#2563EB" stroke-width="2"/>
    <circle cx="0" cy="0" r="6" fill="#2563EB"/>
    
    <!-- 价值固化图标 -->
    <g transform="translate(0, 120)">
      <rect x="-40" y="-20" width="80" height="40" fill="#2563EB" opacity="0.2" rx="20"/>
      <text x="0" y="5" class="content-text" text-anchor="middle" font-size="18" fill="#2563EB">价值固化</text>
    </g>
  </g>
</svg>
