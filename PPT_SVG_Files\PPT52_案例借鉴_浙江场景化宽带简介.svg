<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .model-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #2563EB; font-weight: bold; }
      .insight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #DC2626; font-weight: bold; }
      .scenario-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #059669; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">案例借鉴：浙江"场景化宽带"（简介）</text>
  
  <!-- 模式介绍 -->
  <rect x="100" y="220" width="1720" height="200" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="290" r="20" fill="#2563EB"/>
  <text x="180" y="300" class="content-text" text-anchor="middle" fill="white" font-size="20">模式</text>
  <text x="230" y="270" class="section-title" fill="#2563EB">创新模式：</text>
  <text x="230" y="320" class="model-text">根据用户场景（游戏/直播/教育）</text>
  <text x="230" y="360" class="model-text">打包"带宽+应用+服务"</text>
  <text x="230" y="400" class="content-text">从单一产品向场景解决方案转变</text>
  
  <!-- 场景示例 -->
  <rect x="100" y="440" width="1720" height="300" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="540" r="20" fill="#059669"/>
  <text x="180" y="550" class="content-text" text-anchor="middle" fill="white" font-size="18">场景</text>
  <text x="230" y="520" class="section-title" fill="#059669">典型场景包：</text>
  
  <g transform="translate(280, 560)">
    <!-- 游戏场景 -->
    <rect x="0" y="0" width="400" height="60" fill="#DBEAFE" rx="10"/>
    <text x="20" y="25" class="scenario-text">游戏场景包：</text>
    <text x="20" y="50" class="content-text" font-size="24">千兆宽带+游戏加速+专属客服</text>
    
    <!-- 直播场景 -->
    <rect x="420" y="0" width="400" height="60" fill="#DCFCE7" rx="10"/>
    <text x="440" y="25" class="scenario-text">直播场景包：</text>
    <text x="440" y="50" class="content-text" font-size="24">高速上行+直播工具+技术支持</text>
    
    <!-- 教育场景 -->
    <rect x="840" y="0" width="400" height="60" fill="#FEF3C7" rx="10"/>
    <text x="860" y="25" class="scenario-text">教育场景包：</text>
    <text x="860" y="50" class="content-text" font-size="24">稳定网络+学习应用+家长管控</text>
    
    <!-- 办公场景 -->
    <rect x="0" y="80" width="400" height="60" fill="#FEE2E2" rx="10"/>
    <text x="20" y="105" class="scenario-text">居家办公包：</text>
    <text x="20" y="130" class="content-text" font-size="24">企业级网络+VPN+云存储</text>
    
    <!-- 智能家居场景 -->
    <rect x="420" y="80" width="400" height="60" fill="#F3E8FF" rx="10"/>
    <text x="440" y="105" class="scenario-text">智慧家庭包：</text>
    <text x="440" y="130" class="content-text" font-size="24">物联网+智能设备+统一管理</text>
    
    <!-- 娱乐场景 -->
    <rect x="840" y="80" width="400" height="60" fill="#E0F2FE" rx="10"/>
    <text x="860" y="105" class="scenario-text">影音娱乐包：</text>
    <text x="860" y="130" class="content-text" font-size="24">4K网络+视频会员+音响优化</text>
  </g>
  
  <!-- 启示 -->
  <rect x="100" y="760" width="1720" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="830" r="20" fill="#D97706"/>
  <text x="180" y="840" class="content-text" text-anchor="middle" fill="white" font-size="18">启示</text>
  <text x="230" y="810" class="section-title" fill="#D97706">关键启示：</text>
  <text x="230" y="860" class="insight-text">未来趋势是提供场景化解决方案</text>
  <text x="230" y="900" class="content-text">一线人员要学会识别用户场景需求</text>
  <text x="230" y="940" class="content-text">从"卖宽带"向"卖解决方案"转变</text>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 500)">
    <!-- 场景化图标 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#2563EB" stroke-width="3" opacity="0.3"/>
    
    <!-- 场景图标组合 -->
    <rect x="-60" y="-60" width="40" height="40" fill="#059669" opacity="0.6" rx="5"/>
    <text x="-40" y="-35" class="content-text" text-anchor="middle" fill="white" font-size="14">游戏</text>
    
    <rect x="20" y="-60" width="40" height="40" fill="#D97706" opacity="0.6" rx="5"/>
    <text x="40" y="-35" class="content-text" text-anchor="middle" fill="white" font-size="14">直播</text>
    
    <rect x="-60" y="20" width="40" height="40" fill="#DC2626" opacity="0.6" rx="5"/>
    <text x="-40" y="45" class="content-text" text-anchor="middle" fill="white" font-size="14">教育</text>
    
    <rect x="20" y="20" width="40" height="40" fill="#8B5CF6" opacity="0.6" rx="5"/>
    <text x="40" y="45" class="content-text" text-anchor="middle" fill="white" font-size="14">办公</text>
    
    <text x="0" y="5" class="content-text" text-anchor="middle" font-size="18" fill="#2563EB">场景化</text>
    <text x="0" y="120" class="content-text" text-anchor="middle" font-size="20">场景化宽带</text>
  </g>
</svg>
