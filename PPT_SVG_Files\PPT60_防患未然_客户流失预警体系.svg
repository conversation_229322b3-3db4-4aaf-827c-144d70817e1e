<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .tool-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #2563EB; font-weight: bold; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">防患未然：客户流失预警体系</text>
  
  <!-- 目标 -->
  <rect x="100" y="200" width="1720" height="140" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="250" r="20" fill="#059669"/>
  <text x="180" y="260" class="content-text" text-anchor="middle" fill="white" font-size="20">目标</text>
  <text x="230" y="240" class="section-title" fill="#059669">预警目标：</text>
  <text x="230" y="280" class="content-text">提前识别有流失倾向的用户，主动干预</text>
  <text x="230" y="320" class="highlight-text">变被动救火为主动预防</text>
  
  <!-- 核心工具 -->
  <rect x="100" y="360" width="1720" height="180" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="420" r="20" fill="#2563EB"/>
  <text x="180" y="430" class="content-text" text-anchor="middle" fill="white" font-size="20">工具</text>
  <text x="230" y="400" class="section-title" fill="#2563EB">核心工具：</text>
  <text x="230" y="450" class="tool-text">爱家平台"客户健康度雷达图"</text>
  <text x="230" y="490" class="tool-text">+ 流失预警标签</text>
  <text x="230" y="530" class="content-text">多维度数据分析，智能预警</text>
  
  <!-- 预警维度 -->
  <rect x="100" y="560" width="1720" height="300" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="650" r="20" fill="#D97706"/>
  <text x="180" y="660" class="content-text" text-anchor="middle" fill="white" font-size="18">维度</text>
  <text x="230" y="630" class="section-title" fill="#D97706">预警维度：</text>
  
  <g transform="translate(280, 670)">
    <circle cx="0" cy="0" r="12" fill="#D97706"/>
    <text x="30" y="8" class="content-text">网络质量指标：断网频次、速率波动、WiFi投诉</text>
    
    <circle cx="0" cy="50" r="12" fill="#D97706"/>
    <text x="30" y="58" class="content-text">服务满意度：NPS评分、投诉记录、维修次数</text>
    
    <circle cx="0" cy="100" r="12" fill="#D97706"/>
    <text x="30" y="108" class="content-text">使用活跃度：流量使用、APP登录、缴费行为</text>
    
    <circle cx="0" cy="150" r="12" fill="#D97706"/>
    <text x="30" y="158" class="content-text">竞争环境：区域竞品活动、价格对比、用户咨询</text>
  </g>
  
  <!-- 装饰元素 - 健康度雷达图 -->
  <g transform="translate(1500, 600)">
    <!-- 雷达图背景 -->
    <circle cx="0" cy="0" r="120" fill="none" stroke="#E5E7EB" stroke-width="2"/>
    <circle cx="0" cy="0" r="80" fill="none" stroke="#E5E7EB" stroke-width="2"/>
    <circle cx="0" cy="0" r="40" fill="none" stroke="#E5E7EB" stroke-width="2"/>
    
    <!-- 雷达轴 -->
    <line x1="0" y1="-120" x2="0" y2="120" stroke="#6B7280" stroke-width="1"/>
    <line x1="-120" y1="0" x2="120" y2="0" stroke="#6B7280" stroke-width="1"/>
    <line x1="-85" y1="-85" x2="85" y2="85" stroke="#6B7280" stroke-width="1"/>
    <line x1="85" y1="-85" x2="-85" y2="85" stroke="#6B7280" stroke-width="1"/>
    
    <!-- 健康度区域（示例：中等风险） -->
    <polygon points="0,-80 50,-50 70,20 30,60 -40,40 -60,-30" 
             fill="#D97706" opacity="0.4" stroke="#D97706" stroke-width="2"/>
    
    <!-- 维度标签 -->
    <text x="0" y="-140" class="content-text" text-anchor="middle" font-size="16">网络质量</text>
    <text x="140" y="8" class="content-text" text-anchor="start" font-size="16">服务满意</text>
    <text x="0" y="140" class="content-text" text-anchor="middle" font-size="16">使用活跃</text>
    <text x="-140" y="8" class="content-text" text-anchor="end" font-size="16">竞争环境</text>
    
    <text x="0" y="180" class="highlight-text" text-anchor="middle" font-size="20">中等风险</text>
  </g>
</svg>
