<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .purpose-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #059669; font-weight: bold; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">关键沟通：如何用AR报告与客户沟通？（话术）</text>
  
  <!-- 沟通场景 -->
  <rect x="100" y="220" width="1720" height="500" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  
  <!-- 话术内容 -->
  <text x="150" y="280" class="speech-text">"李女士，根据您家户型模拟</text>
  <text x="150" y="330" class="highlight-text">（展示热力图）</text>
  <text x="150" y="380" class="speech-text">，路由器放这儿，主卧信号可能稍弱（-70dBm）。</text>
  
  <text x="150" y="450" class="speech-text">建议放那个位置，或者考虑加个面板AP，</text>
  <text x="150" y="500" class="speech-text">您看这样全屋绿色覆盖效果最好。"</text>
  
  <!-- 沟通效果 -->
  <rect x="200" y="560" width="1520" height="120" fill="#E0F2FE" stroke="#0284C7" stroke-width="2" rx="15"/>
  <text x="960" y="600" class="purpose-text" text-anchor="middle">沟通效果：</text>
  <text x="960" y="640" class="purpose-text" text-anchor="middle">可视化沟通，引导合理预期/方案选择</text>
  
  <!-- 右侧示意图 -->
  <g transform="translate(1200, 750)">
    <!-- 手机屏幕 -->
    <rect x="0" y="0" width="200" height="300" fill="#1F2937" rx="20"/>
    <rect x="20" y="40" width="160" height="220" fill="#F3F4F6" rx="10"/>
    
    <!-- 热力图模拟 -->
    <rect x="40" y="60" width="120" height="80" fill="#DCFCE7" rx="5"/>
    <text x="100" y="85" class="speech-text" text-anchor="middle" font-size="16">客厅</text>
    <text x="100" y="105" class="speech-text" text-anchor="middle" font-size="14">-45dBm</text>
    <circle cx="100" cy="120" r="15" fill="#059669"/>
    
    <rect x="40" y="160" width="120" height="80" fill="#FEF3C7" rx="5"/>
    <text x="100" y="185" class="speech-text" text-anchor="middle" font-size="16">主卧</text>
    <text x="100" y="205" class="speech-text" text-anchor="middle" font-size="14">-70dBm</text>
    <circle cx="100" cy="220" r="15" fill="#D97706"/>
    
    <!-- 屏幕标题 -->
    <text x="100" y="300" class="speech-text" text-anchor="middle" font-size="18">AR预勘报告</text>
  </g>
  
  <!-- 装饰元素 -->
  <path d="M 1100 400 Q 1150 350 1200 400" class="accent-curve" opacity="0.6"/>
  
  <!-- 关键词提示 -->
  <rect x="100" y="800" width="1000" height="180" fill="#FEF3C7" stroke="#D97706" stroke-width="2" rx="15"/>
  <text x="150" y="840" class="highlight-text">关键沟通要点：</text>
  <text x="200" y="890" class="speech-text">• 用数据说话（具体dBm值）</text>
  <text x="200" y="930" class="speech-text">• 提供解决方案（位置调整/增加设备）</text>
  <text x="200" y="970" class="speech-text">• 让客户参与决策（"您看这样..."）</text>
</svg>
