<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .phase-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #DC2626; font-weight: bold; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #374151; }
      .tool-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2563EB; font-weight: bold; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #059669; font-style: italic; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">阶段一（0-24h）：认知建立 &amp; 基础保障</text>
  
  <!-- 动作 -->
  <rect x="100" y="220" width="1720" height="240" fill="#FEF2F2" stroke="#DC2626" stroke-width="3" rx="20"/>
  <circle cx="180" cy="310" r="20" fill="#DC2626"/>
  <text x="180" y="320" class="content-text" text-anchor="middle" fill="white" font-size="20">动作</text>
  <text x="230" y="280" class="section-title" fill="#DC2626">关键动作：</text>
  
  <g transform="translate(280, 320)">
    <circle cx="0" cy="0" r="12" fill="#DC2626"/>
    <text x="30" y="8" class="content-text">推送《新装指南》（电子版/视频）</text>
    
    <circle cx="0" cy="50" r="12" fill="#DC2626"/>
    <text x="30" y="58" class="content-text">提醒/教学设备绑定APP</text>
    
    <circle cx="0" cy="100" r="12" fill="#DC2626"/>
    <text x="30" y="108" class="content-text">系统自动WiFi快检</text>
    
    <circle cx="0" cy="150" r="12" fill="#DC2626"/>
    <text x="30" y="158" class="content-text">发送欢迎消息和使用提示</text>
  </g>
  
  <!-- 工具 -->
  <rect x="100" y="480" width="1720" height="140" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="530" r="20" fill="#2563EB"/>
  <text x="180" y="540" class="content-text" text-anchor="middle" fill="white" font-size="20">工具</text>
  <text x="230" y="510" class="section-title" fill="#2563EB">核心工具：</text>
  <text x="230" y="550" class="tool-text">蜂鸟消息引擎（自动化推送）</text>
  <text x="230" y="590" class="content-text">根据用户行为自动触发相应消息</text>
  
  <!-- 消息模板示例 -->
  <rect x="100" y="640" width="1720" height="240" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="730" r="20" fill="#059669"/>
  <text x="180" y="740" class="content-text" text-anchor="middle" fill="white" font-size="18">示例</text>
  <text x="230" y="710" class="section-title" fill="#059669">消息模板示例：</text>
  
  <g transform="translate(280, 750)">
    <rect x="0" y="0" width="1200" height="40" fill="#DCFCE7" rx="5"/>
    <text x="20" y="25" class="example-text">欢迎短信："欢迎使用四川移动家庭宽带！点击链接查看新装指南..."</text>
    
    <rect x="0" y="50" width="1200" height="40" fill="#DCFCE7" rx="5"/>
    <text x="20" y="75" class="example-text">绑定提醒："请下载爱家APP绑定您的宽带账号，享受更多便民服务..."</text>
    
    <rect x="0" y="100" width="1200" height="40" fill="#DCFCE7" rx="5"/>
    <text x="20" y="125" class="example-text">快检结果："您的网络运行正常，WiFi信号良好，如有问题请联系..."</text>
  </g>
  
  <!-- 装饰元素 -->
  <g transform="translate(1600, 400)">
    <!-- 24小时时钟 -->
    <circle cx="0" cy="0" r="80" fill="none" stroke="#DC2626" stroke-width="3"/>
    <text x="0" y="-10" class="content-text" text-anchor="middle" font-size="24" fill="#DC2626">0-24h</text>
    <text x="0" y="15" class="content-text" text-anchor="middle" font-size="20" fill="#DC2626">第一阶段</text>
    
    <!-- 时钟指针 -->
    <line x1="0" y1="0" x2="0" y2="-50" stroke="#DC2626" stroke-width="3"/>
    <line x1="0" y1="0" x2="35" y2="0" stroke="#DC2626" stroke-width="2"/>
    <circle cx="0" cy="0" r="6" fill="#DC2626"/>
  </g>
</svg>
