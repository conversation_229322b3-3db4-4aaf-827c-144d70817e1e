<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #059669; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #374151; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #DC2626; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="180" class="title-text">分组与团队建设</text>
  
  <!-- 分组规则 -->
  <rect x="100" y="240" width="1720" height="180" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="300" r="15" fill="#059669"/>
  <text x="230" y="320" class="section-title">分组规则说明</text>
  <text x="230" y="370" class="content-text">按地市/条线进行分组，每组6-8人</text>
  
  <!-- 团队建设 -->
  <rect x="100" y="450" width="1720" height="220" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="510" r="15" fill="#2563EB"/>
  <text x="230" y="530" class="section-title">团队建设要求</text>
  <text x="280" y="580" class="content-text">• 选出组长（负责组织讨论和汇报）</text>
  <text x="280" y="620" class="content-text">• 确定队名（体现团队特色）</text>
  <text x="280" y="660" class="content-text">• 设计口号（激励团队士气）</text>
  
  <!-- 目的说明 -->
  <rect x="100" y="700" width="1720" height="180" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="760" r="15" fill="#D97706"/>
  <text x="230" y="780" class="section-title">活动目的</text>
  <text x="230" y="830" class="content-text">便于后续讨论、演练、竞赛，增强学习互动性</text>
  
  <!-- 装饰元素 - 团队图标 -->
  <g transform="translate(1400, 350)">
    <circle cx="0" cy="0" r="40" fill="#E5E7EB" stroke="#059669" stroke-width="3"/>
    <circle cx="-60" cy="60" r="40" fill="#E5E7EB" stroke="#2563EB" stroke-width="3"/>
    <circle cx="60" cy="60" r="40" fill="#E5E7EB" stroke="#D97706" stroke-width="3"/>
    <circle cx="0" cy="120" r="40" fill="#E5E7EB" stroke="#EF4444" stroke-width="3"/>
  </g>
  
  <!-- 右侧装饰弧线 -->
  <path d="M 1600 500 Q 1700 450 1800 500" class="accent-curve" opacity="0.6"/>
  <path d="M 1600 750 Q 1700 700 1800 750" class="accent-curve" opacity="0.6"/>
</svg>
