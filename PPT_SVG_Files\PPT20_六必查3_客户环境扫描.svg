<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; fill: #DC2626; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; fill: #374151; }
      .method-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; fill: #2563EB; font-weight: bold; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 100 120 Q 960 50 1820 120" class="accent-curve" opacity="0.4"/>
  
  <!-- 标题 -->
  <text x="960" y="160" class="title-text">六必查 3/6：客户环境扫描</text>
  
  <!-- 查什么 -->
  <rect x="100" y="220" width="1720" height="220" fill="#F0FDF4" stroke="#059669" stroke-width="3" rx="20"/>
  <circle cx="180" cy="300" r="15" fill="#059669"/>
  <text x="220" y="280" class="section-title">查什么：</text>
  <text x="220" y="330" class="content-text">• 室内走线路由规划</text>
  <text x="220" y="380" class="content-text">• 电源位置是否合适</text>
  <text x="220" y="430" class="content-text">• 有无干扰源（微波炉、蓝牙设备等）</text>
  
  <!-- 为何查 -->
  <rect x="100" y="460" width="1720" height="180" fill="#EFF6FF" stroke="#2563EB" stroke-width="3" rx="20"/>
  <circle cx="180" cy="530" r="15" fill="#2563EB"/>
  <text x="220" y="510" class="section-title">为何查：</text>
  <text x="220" y="560" class="content-text">影响布线美观、设备取电、WiFi信号质量</text>
  <text x="220" y="610" class="content-text">提前规避环境风险，确保最佳体验</text>
  
  <!-- 怎么查 -->
  <rect x="100" y="660" width="1720" height="200" fill="#FEF3C7" stroke="#D97706" stroke-width="3" rx="20"/>
  <circle cx="180" cy="730" r="15" fill="#D97706"/>
  <text x="220" y="710" class="section-title">怎么查（工具2）：</text>
  <text x="220" y="760" class="method-text">AR预勘测APP + 肉眼观察 + 简单询问</text>
  <text x="220" y="810" class="content-text">科学工具与实地调研相结合</text>
  
  <!-- 装饰元素 - 房屋示意图 -->
  <g transform="translate(1400, 350)">
    <!-- 房屋轮廓 -->
    <rect x="0" y="0" width="400" height="300" fill="none" stroke="#6B7280" stroke-width="3" rx="10"/>
    
    <!-- 房间分割 -->
    <line x1="200" y1="0" x2="200" y2="300" stroke="#6B7280" stroke-width="2"/>
    <line x1="0" y1="150" x2="400" y2="150" stroke="#6B7280" stroke-width="2"/>
    
    <!-- 房间标签 -->
    <text x="100" y="80" class="content-text" text-anchor="middle" font-size="20">客厅</text>
    <text x="300" y="80" class="content-text" text-anchor="middle" font-size="20">卧室</text>
    <text x="100" y="230" class="content-text" text-anchor="middle" font-size="20">厨房</text>
    <text x="300" y="230" class="content-text" text-anchor="middle" font-size="20">卧室</text>
    
    <!-- WiFi信号覆盖示意 -->
    <circle cx="200" cy="150" r="80" fill="#3B82F6" opacity="0.2"/>
    <circle cx="200" cy="150" r="50" fill="#3B82F6" opacity="0.3"/>
    <circle cx="200" cy="150" r="20" fill="#3B82F6" opacity="0.5"/>
    
    <!-- 路由器位置 -->
    <rect x="190" y="140" width="20" height="20" fill="#DC2626"/>
    <text x="200" y="180" class="content-text" text-anchor="middle" font-size="16">路由器</text>
  </g>
</svg>
